#!/usr/bin/env bash
SERVER="old_tech"
BUILD_PATH="build"
DEPLOY_PATH="/var/www/CTR/ets/backend"

yarn
yarn compile
rm -rf $BUILD_PATH
mkdir $BUILD_PATH
cp package.json $BUILD_PATH/
cp package-lock.json $BUILD_PATH/
cp -r lib $BUILD_PATH/
cp -r config $BUILD_PATH/
cp -r public $BUILD_PATH/
# sync build
rsync -avuz $BUILD_PATH/* $SERVER:$DEPLOY_PATH
#rsync -avuz $BUILD_PATH/* $SERVER1:$DEPLOY_PATH
rm -rf $BUILD_PATH
#ssh $SERVER "cd $DEPLOY_PATH/config && mv uat.json default.json && pm2 restart ctr-ets-dev"
ssh $SERVER "cd $DEPLOY_PATH/config && mv dev.json default.json && pm2 restart ctr-ets-dev && pm2 logs ctr-ets-dev"
echo "Done"
