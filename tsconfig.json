{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "src",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@src/*": ["*"],
      "@constants/*": ["constants/*"],
      "@interfaces/*": ["interfaces/*"],
      "@mongoose/*": ["mongoose/*"],
      "@libs/*": ["libs/*"],
      "@config": ["libs/config"],
      "@helper/*": ["libs/helper/*"],
      "@scripts/*": ["scripts/*"],
    },
  }
}
