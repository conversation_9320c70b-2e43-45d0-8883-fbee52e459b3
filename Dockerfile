# Use an official Node.js runtime as a base image
FROM node:17-alpine

# Set the working directory in the container
WORKDIR /app

# Copy package.json and yarn.lock to the working directory
COPY package.json yarn.lock ./

# Install Python, make, and g++ as build dependencies
RUN apk add --update python3 make g++ && rm -rf /var/cache/apk/*

# Install application dependencies using yarn
RUN yarn install

# Copy the rest of the application source code to the working directory
COPY . .

# Expose a port (e.g., 3030) that the application will listen on
EXPOSE 3030

# Define the command to run your application
CMD ["yarn", "uat"]


