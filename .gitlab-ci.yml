stages:
  - test
  - build
  - deploy
test:
  tags:
    - ctr
  stage: test
  script:
    - ls
    - echo $CI_PROJECT_DIR
build:
  tags:
    - ctr
  stage: build
  script:
    - cd $CI_PROJECT_DIR
    - sudo docker-compose build
deploy:
  tags:
    - ctr
  stage: deploy
  script:
    - sudo docker stop trailer-webservice
    - sudo docker rm trailer-webservice
    - sudo docker-compose up -d webservice
