{"name": "ctr-trailer-management-system-be", "description": "A Feathers curd app", "version": "0.0.0", "homepage": "", "private": true, "main": "src", "keywords": ["feathers"], "author": {"name": "To Viet", "email": "<EMAIL>"}, "contributors": [], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "scripts": {"test": "npm run lint && npm run compile && npm run mocha", "lint": "eslint src/. test/. --config .eslintrc.json --ext .ts --fix", "dev": "ts-node-dev --no-notify src/", "start-dev": "swc src -d lib --copy-files && node lib/index.js", "uat": "npm run compile && NODE_ENV=uat node lib/", "start": "npm run compile && NODE_ENV=production node lib/", "deploy": "nodemon src/app.ts", "docs": "apidoc -i src/services/ -o public/docs/", "docs1": "node swagger.js", "mocha": "mocha --require ts-node/register --require source-map-support/register \"test/**/*.ts\" --recursive --exit", "compile": "swc src -d lib --copy-files"}, "standard": {"env": ["mocha"], "ignore": []}, "types": "lib/", "dependencies": {"@aws-sdk/client-s3": "^3.691.0", "@aws-sdk/credential-providers": "^3.687.0", "@aws-sdk/s3-request-presigner": "^3.691.0", "@feathersjs/configuration": "^4.5.12", "@feathersjs/errors": "^4.5.15", "@feathersjs/express": "^4.5.11", "@feathersjs/feathers": "^4.5.11", "@feathersjs/socketio": "^4.5.11", "@feathersjs/transport-commons": "^4.5.11", "@turf/boolean-point-in-polygon": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/turf": "^6.5.0", "@types/bull": "^3.15.8", "@types/cron": "^2.0.0", "@types/joi": "^17.2.3", "@types/lodash": "^4.14.178", "@types/multer-s3": "^2.7.11", "@types/swagger-ui-express": "^4.1.3", "@types/validator": "^13.7.1", "apidoc": "^0.50.5", "await-to-js": "^3.0.0", "aws-sdk": "^2.1168.0", "aws-serverless-express": "^3.4.0", "axios": "^0.24.0", "bull": "^4.8.4", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^2.0.0", "date-fns": "^2.28.0", "dotenv": "^10.0.0", "exceljs": "^4.3.0", "feathers-hooks-common": "^6.0.0", "feathers-memory": "^4.1.0", "feathers-mongoose": "^8.5.1", "feathers-swagger": "^0.5.1", "feathers-sync": "^3.0.3", "helmet": "^4.6.0", "json-as-xlsx": "^2.4.2", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.38", "mongodb-core": "^3.2.7", "mongoose": "^6.0.13", "morgan": "^1.10.0", "multer": "^1.4.4", "multer-s3": "^2.10.0", "nodemon": "^3.0.1", "path": "^0.12.7", "redis": "^4.0.4", "request": "^2.88.2", "require-in-the-middle": "^5.1.0", "serve-favicon": "^2.5.0", "socket.io-parser": "^4.2.0", "swagger-jsdoc": "^6.2.8", "swagger-parser": "^10.0.3", "swagger-typescript-codegen": "^3.2.4", "swagger-ui-express": "^4.6.2", "turf": "^3.0.14", "typescript": "^4.5.4", "uuidv4": "^6.2.12", "validate-typescript": "^4.0.2", "winston": "^3.3.3"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.3.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/jest": "^27.0.3", "@types/serve-favicon": "^2.5.3", "express-swagger-generator": "^1.1.13", "serverless": "^3.1.1", "shx": "^0.3.3", "ts-node-dev": "^1.1.8"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}