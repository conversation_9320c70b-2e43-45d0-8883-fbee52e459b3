#!/usr/bin/env bash
SERVER="cdas_uat_ets_app_0"
SERVER1="cdas_uat_ets_app_1"
BUILD_PATH="build"
DEPLOY_PATH="server"

yarn
yarn compile
rm -rf $BUILD_PATH
mkdir $BUILD_PATH
cp package.json $BUILD_PATH/
cp -r lib $BUILD_PATH/
cp -r config $BUILD_PATH/
cp -r public $BUILD_PATH/
# sync build
rsync -avuz $BUILD_PATH/* $SERVER:$DEPLOY_PATH
# rsync -avuz $BUILD_PATH/* $SERVER1:$DEPLOY_PATH
rm -rf $BUILD_PATH
ssh $SERVER "cd $DEPLOY_PATH/config && mv uat.json default.json && pm2 restart 0 && pm2 logs 0"
# ssh $SERVER1 "cd $DEPLOY_PATH/config && mv uat.json default.json && pm2 restart 0"
echo "Done"
