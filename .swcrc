{
    "jsc": {
        "parser": {
            "syntax": "typescript",
            "tsx": false,
            "dynamicImport": true,
            "decorators": true
        },
        "transform": {
            "legacyDecorator": true,
            "decoratorMetadata": true
        },
        "target": "es2018",
        "externalHelpers": false,
        "keepClassNames": true,
        "loose": false,
        "minify": {
            "compress": false,
            "mangle": false
        },
        "baseUrl": "src",
        "paths": {
          "@src/*": ["*"],
          "@constants/*": ["constants/*"],
          "@interfaces/*": ["interfaces/*"],
          "@mongoose/*": ["mongoose/*"],
          "@libs/*": ["libs/*"],
          "@config": ["libs/config"],
          "@helper/*": ["libs/helper/*"],
          "@scripts/*": ["scripts/*"],
        },
    },
    "module": {
        "type": "commonjs"
    }
}
