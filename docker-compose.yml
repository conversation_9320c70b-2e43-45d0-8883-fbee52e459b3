version: "3.4"
services:

  redis:
    container_name: trailer-redis
    image: redis:4-alpine
    ports:
      - "6379:6379"
    volumes:
      - ./redis-data:/redis/data
    networks:
      - trailer

  webservice:
    container_name: trailer-webservice
    image: node:17-alpine
#    depends_on:
#      - mongodb
#    links:
#      - mongodb
    ports:
      - 3030:3030
    expose:
      - "3030"
    volumes:
      - type: bind
        source: ./
        target: /app
      - type: volume
        source: nodemodules # name of the volume, see below
        target: /app/node_modules
        volume:
          nocopy: true
    working_dir: /app
    command: sh -c 'npm install -f && npm install swc && npm run start-dev'
    networks:
      - trailer


networks:
  trailer:
    external: true

volumes:
  nodemodules:
