{"host": "*************", "env": "local", "port": 3030, "public": "../public/", "paginate": {"default": 10, "max": 1000}, "tncVersion": "1.0.0", "mongodb": "***********************************************************************", "redis": "redis://:vN5gLtLYag@************:6379/0", "urlAuth": "https://psa-ws-eservices-uat.cdaslink.sg/validate-token", "CTRUrl": "https://psa-ws-eservices-uat.cdaslink.sg", "roadUrl": "https://roadmap-uat.cdaslink.sg", "tmsUrl": "http://**************:965", "trackingServiceUrl": "https://psa-tracking-eservices-uat.cdaslink.sg", "aws": {"secretKey": "raekLydM66PAKpb+ewLtNWDfG6+Ag/gUG94jM9J9", "accessKey": "********************", "bucketName": "ets-dev-bucket"}, "awsOptions": {"region": "ap-southeast-1", "secretAccessKey": "raekLydM66PAKpb+ewLtNWDfG6+Ag/gUG94jM9J9", "accessKeyId": "********************", "bucketName": "ets-dev-bucket"}, "redisPort": 6379, "redisHost": "127.0.0.1", "cors": {"origin": ["https://psa-portal-eservices-uat.cdaslink.sg", "http://**************:965", "https://etrailer-uat.cdaslink.sg", "http://localhost:8080", "https://dev.d28ffcuo429wtz.amplifyapp.com", "*"]}, "tokenLifetime": 3600, "oneDayExpire": 86400, "cdasAccount": {"username": "<EMAIL>", "password": "CTR1234567!"}, "trailerMonitor": {"defaultSetting": {"resize": [{"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}], "rearrangement": [{"name": "action", "label": "Actions", "field": "action", "align": "center"}, {"name": "trailerID", "label": "Trailer No.", "field": "trailerNumber", "align": "center"}, {"name": "trailerSize", "label": "Size", "field": "trailerSize", "align": "center"}, {"name": "trailerStatus", "label": "Status", "field": "trailerStatus", "align": "center"}, {"name": "trailerType", "label": "Type", "field": "trailerType", "align": "center"}, {"name": "trailerLocation", "label": "Location", "field": "trailerLocation", "align": "center"}, {"name": "Indicator", "label": "Indicator", "field": "color", "align": "left"}, {"name": "VehicleNo", "label": "Vehicle No", "field": "truckNumber", "align": "center"}, {"name": "Remarks", "label": "Remarks 1", "field": "remarks1", "align": "center"}, {"name": "Remarks", "label": "Remarks 2", "field": "remarks2", "align": "center"}, {"name": "RefDate1", "label": "Ref Date 1", "field": "referenceDateTime1", "align": "center"}, {"name": "RefDate2", "label": "Ref Date 2", "field": "referenceDateTime2", "align": "center"}]}}}