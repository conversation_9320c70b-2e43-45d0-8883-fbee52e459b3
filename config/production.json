{"host": "production", "env": "production", "port": 3030, "public": "../public/", "paginate": {"default": 10, "max": 1000}, "tncVersion": "1.0.0", "mongodb": "mongodb+srv://dbUserProd:<EMAIL>/cdasETRAILER-PROD?retryWrites=true&w=majority", "urlAuth": "https://psa-ws-eservices.cdaslink.sg/validate-token", "CTRUrl": "https://psa-ws-eservices.cdaslink.sg", "roadUrl": "https://roadmap.cdaslink.sg", "tmsUrl": "https://etrailer.cdaslink.sg", "trackingServiceUrl": "https://psa-tracking-eservices.cdaslink.sg", "aws": {"secretKey": "CMOsApybjHnXztivU6mK5YPcrCiJflKysZV2zhTP", "accessKey": "********************", "bucketName": "cdas-prod-etrailer"}, "awsOptions": {"region": "ap-southeast-1", "secretAccessKey": "CMOsApybjHnXztivU6mK5YPcrCiJflKysZV2zhTP", "accessKeyId": "********************", "bucketName": "cdas-prod-etrailer"}, "redis": "redis://***********:6379/0", "redisPort": 6379, "redisHost": "***********", "cors": {"origin": ["https://psa-portal-eservices.cdaslink.sg", "https://etrailer.cdaslink.sg", "https://localhost", "app://localhost"]}, "tokenLifetime": 3600, "oneDayExpire": 86400, "cdasAccount": {"username": "<EMAIL>", "password": "CTR1234567!"}, "trailerMonitor": {"defaultSetting": {"resize": [{"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}, {"min": 0, "max": 100, "size": "8.3333"}], "rearrangement": [{"name": "action", "label": "Actions", "field": "action", "align": "center"}, {"name": "trailerID", "label": "Trailer No.", "field": "trailerNumber", "align": "center"}, {"name": "trailerSize", "label": "Size", "field": "trailerSize", "align": "center"}, {"name": "trailerStatus", "label": "Status", "field": "trailerStatus", "align": "center"}, {"name": "trailerType", "label": "Type", "field": "trailerType", "align": "center"}, {"name": "trailerLocation", "label": "Location", "field": "trailerLocation", "align": "center"}, {"name": "Indicator", "label": "Indicator", "field": "color", "align": "left"}, {"name": "VehicleNo", "label": "Vehicle No", "field": "truckNumber", "align": "center"}, {"name": "Remarks", "label": "Remarks 1", "field": "remarks1", "align": "center"}, {"name": "Remarks", "label": "Remarks 2", "field": "remarks2", "align": "center"}, {"name": "RefDate1", "label": "Ref Date 1", "field": "referenceDateTime1", "align": "center"}, {"name": "RefDate2", "label": "Ref Date 2", "field": "referenceDateTime2", "align": "center"}]}}}