{
  "host": "localhost",
  "env": "uat",
  "port": 3030,
  "public": "../public/",
  "paginate": {
    "default": 10,
    "max": 1000
  },
  "tncVersion": "1.0.0",
  // "mongodb": "mongodb+srv://ETSUserUat:LetMeIn!<EMAIL>/cdasETRAILER?retryWrites=true&w=majority&appName=CDAS-UAT0",
  "mongodb": "mongodb+srv://ETSUserUat:LetMeIn!<EMAIL>/cdasETRAILER?retryWrites=true&w=majority&appName=CDAS-UAT1",
  "urlAuth": "https://psa-ws-eservices-uat.cdaslink.sg/validate-token",
  "CTRUrl": "https://psa-ws-eservices-uat.cdaslink.sg",
  "roadUrl": "https://roadmap-uat.cdaslink.sg",
  "tmsUrl": "https://etrailer-uat.cdaslink.sg",
  "trackingServiceUrl": "https://psa-tracking-eservices-uat.cdaslink.sg",
  "aws": {
    "secretKey": "raekLydM66PAKpb+ewLtNWDfG6+Ag/gUG94jM9J9",
    "accessKey": "********************",
    "bucketName": "ets-dev-bucket"
  },
  "awsOptions": {
    "region":"ap-southeast-1",
    "secretAccessKey": "raekLydM66PAKpb+ewLtNWDfG6+Ag/gUG94jM9J9",
    "accessKeyId": "********************",
    "bucketName": "ets-dev-bucket"
  },
  "redis": "redis://************:6379/0",
  "redisPort": 6379,
  "redisHost": "************",
  "cors": {
    "origin": [
      "https://psa-portal-eservices-uat.cdaslink.sg",
      "http://**************:965",
      "https://etrailer-uat.cdaslink.sg",
      "http://localhost:8080",
      "http://localhost:8081",
      "https://localhost",
      "app://localhost",
      "https://dev.d28ffcuo429wtz.amplifyapp.com",
      "https://psa-portal-eservices.cdaslink.sg",
      "https://etrailer.cdaslink.sg/",
      "*"
    ]
  },
  "tokenLifetime": 3600,
  "oneDayExpire": 86400,
  "cdasAccount": {
    "username": "daniel98",
    "password": "CTR1234567!"
  },
  "trailerMonitor": {
    "defaultSetting": {
      "resize": [
        {
          "min": 10,
          "max": 100,
          "size": 15.***************
        },
        {
          "min": 10,
          "max": 100,
          "size": 13.***************
        },
        {
          "min": 10,
          "max": 100,
          "size": 14.***************
        },
        {
          "min": 10,
          "max": 100,
          "size": 14.***************
        },
        {
          "min": 10,
          "max": 100,
          "size": 14.***************
        },
        {
          "min": 10,
          "max": 100,
          "size": 14.***************
        },
        {
          "min": 10,
          "max": 100,
          "size": 14.***************
        }
      ],
      "rearrangement": [
        {
          "name": "action",
          "label": "Actions",
          "field": "action",
          "align": "center"
        },
        {
          "name": "trailerID",
          "label": "Trailer No.",
          "field": "trailerNumber",
          "align": "left"
        },
        {
          "name": "trailerSize",
          "label": "Size",
          "field": "trailerSize",
          "align": "left"
        },
        {
          "name": "trailerStatus",
          "label": "Status",
          "field": "trailerStatus",
          "align": "left"
        },
        {
          "name": "Indicator",
          "label": "Indicator",
          "field": "color",
          "align": "center"
        },
        {
          "name": "VehicleNo",
          "label": "Vehicle No",
          "field": "truckNumber",
          "align": "center"
        },
        {
          "name": "Remarks",
          "label": "Remarks",
          "field": "remarks",
          "align": "left"
        }
      ]
    }
  }
}
