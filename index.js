const fs = require('fs');
const axios = require('axios');

// Read the file
const fileContent = fs.readFileSync('history.json', 'utf-8');

// Split the file content by newline character and parse each line as JSON
const records = fileContent.split('\n').filter(Boolean).map(JSON.parse);

// export the records to excel file	with the heading
// Trailer Number, Company, LocationName, Coordinates, Date, Time
const excel = require('exceljs');
const workbook = new excel.Workbook();
const worksheet = workbook.addWorksheet('Sheet 1');
worksheet.columns = [
  { header: 'Trailer Number', key: 'trailerNumber', width: 20 },
  { header: 'Company', key: 'company', width: 20 },
  { header: 'LocationName', key: 'locationName', width: 20 },
  { header: 'Coordinates', key: 'coordinates', width: 20 },
  { header: 'Date', key: 'date', width: 20 },
  { header: 'Time', key: 'time', width: 20 },
];

// 	each record is like this
//  _id: { '$oid': '65a5dcbf0383b99cc89fbf9d' },
//     vehicleNo: 'XE1836M',
//     location: '{"type":"Point","coordinates":[103.6359,1.3296]}',
//     trailerNumber: 'TRC1383J',
//     companyId: { '$oid': '608621f16bd7d109630bf0a3' },
//     rssi: '-79',
//     isInsideZone: false,
//     createdAt: { '$date': '2024-01-16T01:32:47.184Z' },
//     updatedAt: { '$date': '2024-01-16T01:32:47.184Z' },
//     __v: 0
//  },

const companies = [
    {
        "_id": "630583b848268a7a36086aac",
        "name": "A-SONIC LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "65576db98370fe64ad4ed64c",
        "name": "ADS LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "63ecab2b1d4bd772c1d98b28",
        "name": "ASIAN WORLDWIDE SERVICES PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "65796bbdb4e3b844a0680cf5",
        "name": "ASSOCIATED CARRIAGE & WAREHOUSING (S) PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6229a5de3318af5ba8282b00",
        "name": "ATS LOGISTICS PTE LTD",
        "status": "active"
    },
    {
        "_id": "617268a73e415537fe1e8852",
        "name": "AW TRANSPORT & WAREHOUSING PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "65d409fdbfa6227acbe48f18",
        "name": "B H S KINETIC PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62bd52ac8740bb42861496ce",
        "name": "CA TPTN & WHSG PTE LTD",
        "status": "active"
    },
    {
        "_id": "64abcbe76aa740369e52fc45",
        "name": "CARGOPORT PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "63ca1b10591a5a3467580d9a",
        "name": "CHELSEA LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "64e46d9e5d1a380fb5c17406",
        "name": "CHUAN LI CONTAINER PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "641920d9d9034d173a67bddc",
        "name": "CITY CONTAINER (S) PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62d6308ab49db96e0a9e2e98",
        "name": "CNT LOGISTICS PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "62c5450be736972a00731ea4",
        "name": "CONSTANT INVADE CONTAINER SERVICES PTE LTD",
        "status": "active"
    },
    {
        "_id": "60cc070964687d093608f806",
        "name": "Container Depot and Logistics Association",
        "status": "active",
        "trailerShareStatus": true
    },
    {
        "_id": "6311a0f2e364e86885e9813e",
        "name": "Container Depot and Logistics Association 2",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "61035f16480072092fe8e82f",
        "name": "CWT INTEGRATED PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "64084f2ac583cf3569977f99",
        "name": "CXL MARINE & LOGISTICS CO PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "655765ea7a816c5fb70cb336",
        "name": "DART GLOBAL FORWARDING PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "65a0f582b551a17cd906cca2",
        "name": "DENZAI HUATIONG LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6214686a09ffe74fc8a279de",
        "name": "DYNA-LOG SINGAPORE PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "6437bcc5c6337934543d4fb4",
        "name": "ENG KONG CONTAINER AGENCIES PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "646330f60b60a543eb25ee9a",
        "name": "EVERISE WAREHOUSING & TRANSPORTATION (PTE) LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "65575d257ca7a156f3715f75",
        "name": "FPS GLOBAL LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "655608c983dabd24c52c8875",
        "name": "FS FREIGHT SYSTEMS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6556ebf9459eef5ff846c9c4",
        "name": "G.H.SEOW TRANSPORTATION PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "655b01e987992b7757bad27f",
        "name": "GALAXY LOGISTICS PTE LTD",
        "status": "inactive",
        "trailerShareStatus": false
    },
    {
        "_id": "6287364ff7b25e33d70c0021",
        "name": "GMC GROUP PTE LTD",
        "status": "active"
    },
    {
        "_id": "6555c5319f98ed66d9978190",
        "name": "GOODWIND TRANSPORT CO PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62296c4b3318af5ba80fe82d",
        "name": "HANG SENG CONTAINER & TRANSPORT SERVICES",
        "status": "active"
    },
    {
        "_id": "651140f9c21f3f779d48b5e7",
        "name": "HARBOUR HANDLERS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6364d330dd753d62aa56d1a2",
        "name": "HE YI TRANSPORTATION PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62d629f689eb663b473b9d26",
        "name": "HG LOGISTICS PTE. LTD",
        "status": "active"
    },
    {
        "_id": "632c376acbd8f95150024a8f",
        "name": "HOCK SENG HENG TRANSPORT & TRADING PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62a808b94ec7073bcbe1217b",
        "name": "KIM SOON LEE PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "6243ff0824bce853752c1112",
        "name": "KL LOGISTICS PTE LTD",
        "status": "active"
    },
    {
        "_id": "6273289b7735da4d502dc2aa",
        "name": "KLEIO-ONE SOLUTION PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "61036295480072092fe8eca4",
        "name": "LCH LOGISTICS PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "63c4e38b6c3ab56ce712a5ab",
        "name": "LEGEND INTEGRATED LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6570423789f3c248085ba411",
        "name": "LENG HUAT TRANSPORT COMPANY",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "639fd52cbed35b3dee8552a5",
        "name": "LIKOK LOGISTICS PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6446479ce813ba511b3713a6",
        "name": "LIM GUAN TEH LOGISTICS PTE LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "65951b21ec7d7828fc95847c",
        "name": "MAJESTY EXPRESS PTE LTD",
        "status": "inactive",
        "trailerShareStatus": false
    },
    {
        "_id": "62f9fb72f3476013240349b4",
        "name": "MASINDO SUPPLY CHAIN (S) PTE. LTD.",
        "status": "inactive",
        "trailerShareStatus": false
    },
    {
        "_id": "65558da013c2a3605255fd52",
        "name": "MCS LOGISTICS (S) PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6308438f0581132ebd0da2a8",
        "name": "MEGASTAR SHIPPING PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "644655817690ab46684a8239",
        "name": "MENG LEE TRANSPORT PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62304da7fd451409700af543",
        "name": "NEK LOGISTICS PTE LTD",
        "status": "active"
    },
    {
        "_id": "65266a13f162217743fd9d81",
        "name": "PACIFIC INTEGRATED LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "64119962f9d0af68cfa74c83",
        "name": "PALTRANS LOGISTICS PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6222f45b0d0a9451291b34b7",
        "name": "PAN OCEAN TRANSPORT PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "647d4f42d8857e4427a0f6d8",
        "name": "PO SAN TRANSPORTATION PTE LTD",
        "status": "inactive",
        "trailerShareStatus": false
    },
    {
        "_id": "62b133d8d5bbb80221ff92e4",
        "name": "POH TIONG CHOON LOGISTICS LIMITED",
        "status": "active"
    },
    {
        "_id": "623bf4328ef7d450f394857f",
        "name": "PTC EXPRESS PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "6539d6310f617d189c4016e6",
        "name": "QY Test Company",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6450f1ca1953f221c5d74842",
        "name": "REJOICE CONTAINER SERVICES PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "63a015f2a341986509c57f75",
        "name": "REVOCHEM INDUSTRIES PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "635b834258bf4f3e53d64b6c",
        "name": "RONG DE DISTRIBUTION PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "660d343e378fe9684545c421",
        "name": "S.M.S. STEVEDORING PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6471a6606a20c0660fcecee2",
        "name": "SAMBE WAREHOUSING & TRANSPORTATION PTE.LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6268ef78593e12648495db59",
        "name": "SG INTEGRATED PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "65855ba0e698823e2f4cd7fe",
        "name": "SG SAGAWA AMEROID PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "624d1343c2d7da43fa0ca716",
        "name": "SH COGENT LOGISTICS PTE LTD",
        "status": "active"
    },
    {
        "_id": "65641474286a39647f14aadb",
        "name": "SIGMA CONTAINER LINE PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "633aa7a6ccee635169201107",
        "name": "SINGAPORE TRANSPORT SUPPLY SERVICE (PRIVATE) LIMITED",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "64a3ded190fdd151b2e8d873",
        "name": "SKK FREIGHT SERVICES PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6461e8b85778fa768e659462",
        "name": "SMART RELOCATORS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6491679b24ebf24ae911e5a6",
        "name": "SMP GLOBAL PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "656031f76beed353731b9717",
        "name": "SNL LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6232a90b8b8cce637ebb6d80",
        "name": "SOON NAM HENG LOGISTICS PTE LTD",
        "status": "active"
    },
    {
        "_id": "628600c10d2aa151f63ba9bb",
        "name": "STORBEST-SSHK COLD LOGISTICS PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "62298da0bb777259a2003ea4",
        "name": "TANIA ASIA SERVICES PTE LTD",
        "status": "active"
    },
    {
        "_id": "6286eb8a9206903d2d4cb845",
        "name": "TANNY SERVICES PTE LTD",
        "status": "active"
    },
    {
        "_id": "6572e3167fb4875f3c92286e",
        "name": "TDS LOGISTICS PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "61fb5a2413f2a915242886cf",
        "name": "TEL TRANSPORT & WAREHOUSING PTE. LTD.",
        "status": "active"
    },
    {
        "_id": "656037492a443d1753cf7930",
        "name": "TIONG AIK HENG TRADING & SERVICES PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "62425bbec8c5986c32115ddc",
        "name": "TNL EXPRESS PTE LTD",
        "status": "active"
    },
    {
        "_id": "63eee2949d944d57442ee2cf",
        "name": "TONG CONTAINERS DEPOT (S) PTE LTD",
        "status": "inactive",
        "trailerShareStatus": false
    },
    {
        "_id": "651fe7017d034f3fc6a443c6",
        "name": "TONG HONG LEE ENGINEERING PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "626613fb0dc7d61262aedd50",
        "name": "UBTS PTE LTD",
        "status": "active"
    },
    {
        "_id": "61ce75ed6060a41a0dade99f",
        "name": "UNION SERVICES (S’PORE) PTE LTD",
        "status": "active"
    },
    {
        "_id": "63fd90ae750a6e6ea5ae5053",
        "name": "UNITED FMO PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "61d4270f722fea20e767e467",
        "name": "WENDY TRANSPORT ENTERPRISE COMPANY",
        "status": "active"
    },
    {
        "_id": "61d39c95637e1d7127c5ec6a",
        "name": "WING SENG LOGISTICS PTE LTD",
        "status": "active"
    },
    {
        "_id": "662b5bdc0475e25be0d31c49",
        "name": "WYN2000 TRANSPORT & CONTAINER SERVICES PTE LTD",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6556f30f2b7b6e5baacb37c8",
        "name": "YEW CHOON PTE. LTD.",
        "status": "active",
        "trailerShareStatus": false
    },
    {
        "_id": "6539d561697bb03f9b3724f7",
        "name": "YQ Test Company",
        "status": "active",
        "trailerShareStatus": false
    }
]

// curl --location 'https://roadmap-uat.cdaslink.sg/road-map/name' \
// --header 'Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MTQ5Nzg1MzYsImlhdCI6MTcxNDk3ODUzNiwiZXhwIjoxNzE1MTUxMzM2LCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI1ZmQ4NjFhMzBlOGU3NzYwYzE5YjZlOWMiLCJqdGkiOiJkYWVjMmJmOC1kNjMzLTQ4ZTEtYjIzZS01ODIwNmJjODFlZDAifQ.DPaI-G8gmOjie_rSu3y5rnCCaPz2mIF_n2t3GQP6tKM' \
// --header 'Content-Type: application/json' \
// --header 'Cookie: AWSALB=5ZgFhcyzwxNYoKR2dajeb0hkmE+9feRWw1mEH3Cha3hTJqui4TOLIPapihnB6t30Hh4F7sUF1ujuevtIAzCQPP4phL6bkwSsMwyxHfrOvrpc5w8gJ03Dl6Pynnvo; AWSALBCORS=5ZgFhcyzwxNYoKR2dajeb0hkmE+9feRWw1mEH3Cha3hTJqui4TOLIPapihnB6t30Hh4F7sUF1ujuevtIAzCQPP4phL6bkwSsMwyxHfrOvrpc5w8gJ03Dl6Pynnvo' \
// --data '{
//     "latitude": "1.3048",
//     "longitude": "103.6272"
// }'

// based on this curl write a request to get the location name from the coordinates
// and add it to the excel sheet
// locationName: record.locationName,
// coordinates: JSON.parse(record.location).coordinates,
async function hello() {
	const batchSize = 2000;
	const delay = 10000; // 1 minute

	const batches = Math.ceil(records.length / batchSize);

	for (let i = 0; i < batches; i++) {
		const start = i * batchSize;
		const end = start + batchSize;
		const batch = records.slice(start, end);

		await Promise.all(
			batch.map(async (record) => {
				const coordinates = JSON.parse(record.location).coordinates;
				let locationName = 'NOT_FOUND';

				let options = {
					method: 'POST',
					url: 'https://roadmap.cdaslink.sg/road-map/name',
					headers: {
						Authorization:
							'eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MTUyNDYxMzEsImlhdCI6MTcxNTI0NjEzMSwiZXhwIjoxNzE1ODUwOTMxLCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MmRiNjQwZWFhYjAwYjRlOTVhZjA5NTIiLCJqdGkiOiI3NTMwMTMwOS02MDcxLTQzZjgtOWFhYy04MzI5MmNjZTVlN2UifQ.NtzfnxXAExL6XA8oo7BkZxXSSOschEQIx-KxbBOnGl4',
						'content-type': 'application/json',
						'cache-control': 'no-cache',
					},
					data: {
						latitude: coordinates[1] + '',
						longitude: coordinates[0] + '',
					},
				};

				let retryCount = 0;
				let result;
				while (retryCount < 3) {
					try {
						result = await axios(options);
						if (result.data.name === 'NOT_FOUND') throw new Error('Location not found');
						break;
					} catch (error) {
						console.log('Error occurred:', error.message);
						retryCount++;
						await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 3 seconds before retrying
					}
				}

				    locationName = result?.data?.name || 'NOT_FOUND';
					console.log('LOG-locationName', locationName);

                    worksheet.addRow({
                        trailerNumber: record.trailerNumber,
                        company: companies.find(
                            (company) => company._id === record.companyId.$oid
                        ).name,
                        locationName,
                        coordinates: JSON.parse(record.location).coordinates,
                        date: new Date(record.createdAt.$date).toLocaleDateString('en-GB', { timeZone: 'Asia/Singapore' }),
                        time: new Date(record.createdAt.$date).toLocaleTimeString('en-GB', { hour12: false, timeZone: 'Asia/Singapore' })
                    });
			})
		);


        // Save the workbook
        const fileName = `history_${i}.xlsx`;
        await workbook.xlsx.writeFile(fileName);
	}
}

hello();

