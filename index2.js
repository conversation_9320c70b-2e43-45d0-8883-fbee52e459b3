const xlsx = require('xlsx');
const axios = require('axios');

const workbook = xlsx.readFile('draw.xlsx');
const sheet_name_list = workbook.SheetNames;
const data = xlsx.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]]);

const notFoundRecords = data.filter(record => record.LocationName === 'NOT_FOUND');
console.log('LOG-notFoundRecords.length', notFoundRecords.length);

const processRecord = async (record) => {
    const str = record.Coordinates;
    const regex = /\[([^\]]+)\]/; // Regular expression to match anything inside square brackets
    const match = str.match(regex);
        const coordinates = match[1].split(',').map(Number); // Split the matched string by comma and convert to numbers
        const longitude = coordinates[0];
        const latitude = coordinates[1];
    let options = {
        method: 'POST',
        url: 'https://roadmap.cdaslink.sg/road-map/name',
        headers: {
            Authorization: 'eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MTUyNDYxMzEsImlhdCI6MTcxNTI0NjEzMSwiZXhwIjoxNzE1ODUwOTMxLCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MmRiNjQwZWFhYjAwYjRlOTVhZjA5NTIiLCJqdGkiOiI3NTMwMTMwOS02MDcxLTQzZjgtOWFhYy04MzI5MmNjZTVlN2UifQ.NtzfnxXAExL6XA8oo7BkZxXSSOschEQIx-KxbBOnGl4',
            'content-type': 'application/json',
            'cache-control': 'no-cache',
        },
        data: {
            latitude: latitude + '',
            longitude: longitude + '',
        },
    };
    let retryCount = 0;
    let result;
    while (retryCount < 3) {
        try {
            result = await axios(options);
            if (result.data.name === 'NOT_FOUND') throw new Error('Location not found');
            record.LocationName = result.data.name; // Update the LocationName in the record
            break;
        } catch (error) {
            console.log('Error occurred:', error.message);
            retryCount++;
            await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 1 second before retrying
        }
    }
    return record; // Return the updated record
};

const processRecords = async (records) => {
    const updatedRecords = await Promise.all(records.map(processRecord));
    return updatedRecords;
};

const foundRecords = data.filter(record => record.LocationName !== 'NOT_FOUND');
console.log('LOG-foundRecords.length', foundRecords.length);

const chunkArray = (array, chunkSize) => {
    let index = 0;
    let arrayLength = array.length;
    let tempArray = [];
  
    for (index = 0; index < arrayLength; index += chunkSize) {
        let chunk = array.slice(index, index+chunkSize);
        tempArray.push(chunk);
    }
  
    return tempArray;
};

// Split notFoundRecords into chunks of 2000 records each
const recordChunks = chunkArray(notFoundRecords, 10000);

async function hello() {
    const newWorkbook = xlsx.utils.book_new();
            const newWorksheet = xlsx.utils.json_to_sheet(foundRecords);
            xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, 'Sheet1');
            xlsx.writeFile(newWorkbook, `found_records_${1}.xlsx`);

    for (let i = 0; i < recordChunks.length; i++) {
        const chunk = recordChunks[i];
        console.log('LOG-chunk.length', chunk.length);
    // Assuming this code is inside an async function
        try {
            const updatedRecords = await processRecords(chunk);
            const newWorkbook = xlsx.utils.book_new();
            const newWorksheet = xlsx.utils.json_to_sheet(updatedRecords);
            xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, 'Sheet1');
            xlsx.writeFile(newWorkbook, `found_records_${i}.xlsx`);
        } catch (error) {
            console.error('Error processing records:', error);
        }
    }
}
hello();