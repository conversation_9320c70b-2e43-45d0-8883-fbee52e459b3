const rentalSchema = {
  Rental: {
    type: 'object',
    properties: {
      trailerRentalId: { type: 'string', example: '61e40740f77ee31e3c3e24db' },
      rentee: {
        type: 'object',
        properties: {
          name: { type: 'string', example: 'ABC Company' },
          email: { type: 'string', example: '<EMAIL>' },
          company: { type: 'string', example: 'ABC Company' }
        },
        required: ['name', 'email', 'company']
      },
      owner: {
        type: 'object',
        properties: {
          name: { type: 'string', example: 'XYZ Company' },
          email: { type: 'string', example: '<EMAIL>' },
          company: { type: 'string', example: 'XYZ Company' }
        },
        required: ['name', 'email', 'company']
      },
      start: { type: 'string', format: 'date', example: '2023-04-01' },
      end: { type: 'string', format: 'date', example: '2023-04-10' },
      approved: { type: 'boolean', default: false },
      inspections: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['handover', 'takeover'],
              example: 'handover'
            },
            date: { type: 'string', format: 'date', example: '2023-04-01' },
            inspector: {
              type: 'object',
              properties: {
                _id: { type: 'string', example: '61e40740f77ee31e3c3e24db' },
                name: { type: 'string', example: 'John Doe' },
                email: { type: 'string', example: '<EMAIL>' },
                company: { type: 'string', example: 'ABC Company' }
              },
              required: ['_id', 'name', 'email', 'company']
            },
            damageReport: { type: 'string', example: '61e40740f77ee31e3c3e24db' }
          },
          required: ['type', 'date', 'inspector']
        }
      }
    },
    required: ['rentee', 'owner', 'start', 'end', 'inspections']
  }
}

const rentalRequestSchema = {
  RentalRequest: {
    type: 'object',
    properties: {
      trailerRentalId: { type: 'string', format: 'uuid', example: '61e40740f77ee31e3c3e24db', description: 'The id of the trailer rental' },
      requester: {
        type: 'object',
        properties: {
          name: { type: 'string', example: 'John Doe' },
          email: { type: 'string', example: '<EMAIL>' },
          company: { type: 'string', format: 'uuid', example: '61e40740f77ee31e3c3e24db' }
        }
      },
      requestStart: { type: 'string', format: 'date-time', example: '2023-04-01T00:00:00Z' },
      requestEnd: { type: 'string', format: 'date-time', example: '2023-04-10T00:00:00Z' },
      reason: { type: 'string', example: 'Family vacation' },
      status: { type: 'string', enum: ['Pending', 'Approved', 'Rejected'], default: 'Pending', example: 'Pending' },
      requestType: { type: 'string', enum: ['New' , 'Extension'], default: 'New' },
    },
    example: {
      trailerRentalId: '61e40740f77ee31e3c3e24db',
      requester: {
        name: 'John Doe',
        email: '<EMAIL>',
        company: '61e40740f77ee31e3c3e24db'
      },
      requestStart: '2023-04-01T00:00:00Z',
      requestEnd: '2023-04-10T00:00:00Z',
      reason: 'Family vacation',
      requestType: 'New'
    }
  }
}

const trailerRentalSchema = {
  TrailerRental: {
    type: 'object',
    required: [
      'trailer',
      'ownerId',
      'rentingDetails'
    ],
    properties: {
      trailer: {
        type: 'object',
        required: [
          'type'
        ],
        properties: {
          'type': {
            'type': 'string'
          }
        },
        description: 'The trailer details'
      },
      ownerId: {
        'type': 'string',
        'description': 'The ID of the owner of the trailer'
      },
      availableForRentTo: {
        type: 'array',
        items: {
          type: 'string'
        },
        description: 'The list of IDs of users who can rent the trailer'
      },
      rentingDetails: {
        type: 'object',
        properties: {
          dailyPrice: {
            type: 'number'
          },
          monthlyPrice: {
            type: 'number'
          },
          availableFrom: {
            type: 'string',
            format: 'date-time'
          },
          availableTo: {
            type: 'string',
            format: 'date-time'
          }
        },
        required: [
          'dailyPrice',
          'monthlyPrice',
          'availableFrom',
          'availableTo'
        ],
        description: 'The rental details of the trailer'
      },
      rentingHistory: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            rentedBy: {
              type: 'object',
              required: [
                'name',
                'email'
              ],
              properties: {
                name: {
                  'type': 'string'
                },
                email: {
                  'type': 'string'
                },
                company: {
                  'type': 'string'
                }
              },
              description: 'The user who rented the trailer'
            },
            rentedFrom: {
              type: 'string',
              format: 'date-time',
              description: 'The start date of the rental period'
            },
            rentedTo: {
              type: 'string',
              format: 'date-time',
              description: 'The end date of the rental period'
            },
            rentedPrice: {
              type: 'number',
              description: 'The price paid for the rental period'
            },
            status: {
              type: 'string',
              enum: [
                'available',
                'unavailable'
              ],
              default: 'available',
              description: 'The availability status of the trailer'
            }
          },
          required: [
            'rentedBy',
            'rentedFrom',
            'rentedTo',
            'rentedPrice'
          ],
          description: 'The rental history of the trailer'
        },
        description: 'The rental history of the trailer'
      }
    },
    description: 'Schema for a trailer rental'
  }
}

const inspectionSchema = {
  Inspection: {
    type: 'object',
    properties: {
      type: { type: String, required: true, enum: ['handover', 'takeover'] },
      status: { type: String, required: true, enum: ['NotStarted', 'InProgress', 'Completed'], default: 'NotStarted' },
      damageReport: { type: String, required: false },
      inspector: {
        _id: { type: String, required: true },
        name: { type: String, required: true },
        email: { type: String},
        company: { type: String, required: true }
      },
      trailerRentalId: { type: String, required: true }
    },
    example: {
      type: 'handover',
      status: 'NotStarted',
      damageReport: 'Minor scratches on the left side',
      inspector: {
        _id: '1234567890',
        name: 'John Smith',
        email: '<EMAIL>',
        company: '9876543210'
      },
      trailerRentalId: 'abcd1234'
    }
  }
}


const security = {
  securitySchemes: {
    bearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    }
  }
}
module.exports = {
  schemas: {
    ...rentalSchema,
    ...rentalRequestSchema,
    ...trailerRentalSchema,
    ...inspectionSchema,
  }
}
