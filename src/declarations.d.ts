import { Application as ExpressFeathers } from '@feathersjs/express';

// A mapping of service names to types. Will be extended in service files.
export interface ServiceTypes {}

export interface ETSApplication extends ExpressFeathers<ServiceTypes> {
  getCDASToken: () => string; // get the CDAS token
}

// The application instance type that will be used everywhere else
export type Application = ETSApplication;
