// Initializes the `trailers` service on path `/trailer-rental-availability`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Rental } from './rental.class'
import hooks from './rental.hooks'
import createModel from '../../models/rentals.model'
import authAPI from '../../middleware/api-auth'
import { success } from '../../helper/response'
import * as Response from '../../helper/response'
import { CTVRental } from '../../models/rentals.model'
import { BadRequest } from '@feathersjs/errors'
import { isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'
import { sendErrorToWebhook } from '../../helper/helper'
import mongoose from 'mongoose'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    rental: Rental & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['remove'],
    whitelist: [
      '$text',
      '$search',
      '$regex',
      '$exists',
      '$options',
      '$elemMatch',
      '$populate',
      '$push',
      '$set'
    ]
  }

  app.post(
    '/rental/:id/reject',
    authAPI,
    isAccessibleDMS,
    async (req: any, res: any) => {
      const startTime = Date.now()
      const requestId = req.params.id

      console.log(`[REJECT API] Starting rental rejection for ID: ${requestId}`)
      console.log(`[REJECT API] Request user:`, {
        userId: req.user?.id,
        userCompany: req.user?.company,
        userEmail: req.user?.email
      })

      try {
        // Step 1: Fetch rental
        console.log(`[REJECT API] Fetching rental with ID: ${requestId}`)
        const rental = await service._get(req.params.id)

        if (!rental) {
          console.error(`[REJECT API] Rental not found for ID: ${requestId}`)
          throw new BadRequest('Rental not found')
        }

        console.log(`[REJECT API] Rental found:`, {
          id: rental._id,
          status: rental.status,
          trailerNumber: rental.trailerNumber,
          ownerCompany: rental.owner?.company,
          renteeCompany: rental.rentee?.company,
          ownerEmail: rental.owner?.email,
          renteeName: rental.rentee?.name
        })

        // Step 2: Validate status
        console.log(`[REJECT API] Validating rental status. Current: ${rental.status}, Required: ${CTVRental.status.NotStarted}`)
        if (rental.status !== CTVRental.status.NotStarted) {
          console.error(`[REJECT API] Invalid status for rejection. Current: ${rental.status}`)
          throw new BadRequest('Current status is not available for reject')
        }

        // Step 3: Update rental status
        console.log(`[REJECT API] Updating rental status to Rejected`)
        const result = await service.patch(req.params.id, {
          status: CTVRental.status.Rejected
        }, req)

        console.log(`[REJECT API] Rental status updated successfully:`, {
          id: result._id,
          newStatus: result.status
        })

        // Step 4: Send notification
        console.log(`[REJECT API] Sending notification email`)
        try {
          const notificationData = {
            type: 'email',
            subject: 'New Renting Request',
            receiver: rental.owner.email,
            typeStorage: 'New Request',
            companyId: rental.owner.company,
            content: `The rental of trailer ${rental.trailerNumber} was canceled by ${rental.rentee.name}`
          }

          console.log(`[REJECT API] Notification data:`, {
            type: notificationData.type,
            subject: notificationData.subject,
            receiver: notificationData.receiver,
            companyId: notificationData.companyId,
            contentLength: notificationData.content?.length
          })

          // @ts-ignore
          await app.sendCustomNotification(notificationData)
          console.log(`[REJECT API] Notification sent successfully`)

        } catch(notificationError: any) {
          console.error(`[REJECT API] Notification error:`, {
            message: notificationError.message,
            stack: notificationError.stack,
            response: notificationError.response?.data
          })
          // Don't throw here - notification failure shouldn't fail the rejection
        }

        const duration = Date.now() - startTime
        console.log(`[REJECT API] Rental rejection completed successfully in ${duration}ms`)

        Response.success(res, result, 200)

      } catch (error: any) {
        const duration = Date.now() - startTime
        console.error(`[REJECT API] Error occurred after ${duration}ms:`, {
          message: error.message,
          stack: error.stack,
          code: error.code,
          name: error.name,
          rentalId: requestId
        })

        // Log additional context for debugging
        if (error.response) {
          console.error(`[REJECT API] Error response:`, {
            status: error.response.status,
            data: error.response.data
          })
        }

        Response.error(res, error, error.code)
      }
    }
  )

  app.get(
    '/rental/incoming',
    authAPI,
    isAccessibleDMS,
    async (req: any, res: any) => {
      const { query, user, headers } = req
      const params = { user, query, headers }
      if (user.company || query?.company) {
        params.query = {
          ...(query && { ...query }),
          'owner.company': user.company || query?.company
        }
      }
      try {
        const result = await service.find(params)
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error(
          `Error while retrieving upcoming rental: ${error.message}`
        )
        console.error(error.stack)
        Response.error(res, error, error.code)
      }
    }
  )

  app.get(
    '/rental/outgoing',
    authAPI,
    isAccessibleDMS,
    async (req: any, res: any) => {
      const { query, user, headers } = req
      const params = { user, query, headers }
      if (user.company || query?.company) {
        params.query = {
          ...(query && { ...query }),
          'rentee.company': user.company || query?.company
        }
      }
      try {
        const result = await service.find(params)
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error(
          `Error while retrieving outgoing rental: ${error.message}`
        )
        console.error(error.stack)
        Response.error(res, error, error.code)
      }
    }
  )

  app.get('/rental/assigned', authAPI, async (req: any, res: any) => {
    const { query, user, headers } = req
    const params = { user, query, headers }
    try {
      if (user.company) {
        params.query['assignee._id'] = {
          $in: [
            user._id,
            user._id.toString(),
            new mongoose.Types.ObjectId(user._id)
          ]
        }
        // params.query['assignee._id'] = new mongoose.Types.ObjectId(user._id) // new:
      }
      const result = await service._find(params)
      res.json(result)
    } catch (error: any) {
      await sendErrorToWebhook(`Error get assigned trailer:`, {
        params,
        error: error.message
      })
      console.error(`Error get assigned trailer:`, {
        error: error.message,
        params
      })
      console.error(error.stack)
      Response.error(res, error, error.code)
    }
  })

  app.get('/rental/statuses', authAPI, async (req: any, res: any) => {
    const { query, user, headers } = req
    const params = { user, query, headers }
    try {
      if (user.company) {
        params.query['owner.company'] = user.company
        // add query status is in processing status
      }
      params.query.$limit = 1000
      params.query.status = {
        $in: CTVRental.getProcessingStatus()
      }
      const trailerRentalService = app.service('trailer-rental-availability')
      const response = {};
      const trailerRental = await trailerRentalService._find({
        query: {
          ownerId: user.company
        }
      })
      trailerRental.data.map((item: any) => {
        response[item.trailerNumber] = 'listing'
      })
      const result = await service._find(params)
      // show only status and trailer number
      result.data.map((item: any) => {
        response[item.trailerNumber] = item.status
      })
      res.json(response)
    } catch (error: any) {
      console.error(`Error get statuses trailer:`, {
        error: error.message,
        params
      })
      Response.error(res, error, error.code)
    }
  })

  // Initialize our service with any options it requires
  app.use('/rental', new Rental(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('rental')
  service.hooks(hooks)
}
