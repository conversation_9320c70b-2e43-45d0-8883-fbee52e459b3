// Initializes the `trailers` service on path `/trailer-rental-availability`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Rental } from './rental.class'
import hooks from './rental.hooks'
import createModel from '../../models/rentals.model'
import authAPI from '../../middleware/api-auth'
import { success } from '../../helper/response'
import * as Response from '../../helper/response'
import { CTVRental } from '../../models/rentals.model'
import { BadRequest } from '@feathersjs/errors'
import { isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'
import { sendErrorToWebhook } from '../../helper/helper'
import mongoose from 'mongoose'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    rental: Rental & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['remove'],
    whitelist: [
      '$text',
      '$search',
      '$regex',
      '$exists',
      '$options',
      '$elemMatch',
      '$populate',
      '$push',
      '$set'
    ]
  }

  app.post(
    '/rental/:id/reject',
    authAPI,
    isAccessibleDMS,
    async (req: any, res: any) => {
      try {
        const rental = await service._get(req.params.id)
        console.log('rental', !!rental)
        if (rental.status !== CTVRental.status.NotStarted)
          throw new BadRequest('Current status is not available for reject')
        const result = await service.patch(req.params.id, {
          status: CTVRental.status.Rejected
        }, req)
        try {
          // @ts-ignore
          await app.sendCustomNotification({
            type: 'email',
            subject: 'New Renting Request',
            receiver: rental.owner.email,
            typeStorage: 'New Request',
            companyId: rental.owner.company,
            content: `The rental of trailer ${rental.trailerNumber} was canceled by ${rental.rentee.name}`
          })
        } catch(err) {
          console.log('err', err)
        }
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error(error.message)
        Response.error(res, error, error.code)
      }
    }
  )

  app.get(
    '/rental/incoming',
    authAPI,
    isAccessibleDMS,
    async (req: any, res: any) => {
      const { query, user, headers } = req
      const params = { user, query, headers }
      if (user.company || query?.company) {
        params.query = {
          ...(query && { ...query }),
          'owner.company': user.company || query?.company
        }
      }
      try {
        const result = await service.find(params)
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error(
          `Error while retrieving upcoming rental: ${error.message}`
        )
        console.error(error.stack)
        Response.error(res, error, error.code)
      }
    }
  )

  app.get(
    '/rental/outgoing',
    authAPI,
    isAccessibleDMS,
    async (req: any, res: any) => {
      const { query, user, headers } = req
      const params = { user, query, headers }
      if (user.company || query?.company) {
        params.query = {
          ...(query && { ...query }),
          'rentee.company': user.company || query?.company
        }
      }
      try {
        const result = await service.find(params)
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error(
          `Error while retrieving outgoing rental: ${error.message}`
        )
        console.error(error.stack)
        Response.error(res, error, error.code)
      }
    }
  )

  app.get('/rental/assigned', authAPI, async (req: any, res: any) => {
    const { query, user, headers } = req
    const params = { user, query, headers }
    try {
      if (user.company) {
        params.query['assignee._id'] = {
          $in: [
            user._id,
            user._id.toString(),
            new mongoose.Types.ObjectId(user._id)
          ]
        }
        // params.query['assignee._id'] = new mongoose.Types.ObjectId(user._id) // new:
      }
      const result = await service._find(params)
      res.json(result)
    } catch (error: any) {
      await sendErrorToWebhook(`Error get assigned trailer:`, {
        params,
        error: error.message
      })
      console.error(`Error get assigned trailer:`, {
        error: error.message,
        params
      })
      console.error(error.stack)
      Response.error(res, error, error.code)
    }
  })

  app.get('/rental/statuses', authAPI, async (req: any, res: any) => {
    const { query, user, headers } = req
    const params = { user, query, headers }
    try {
      if (user.company) {
        params.query['owner.company'] = user.company
        // add query status is in processing status
      }
      params.query.$limit = 1000
      params.query.status = {
        $in: CTVRental.getProcessingStatus()
      }
      const trailerRentalService = app.service('trailer-rental-availability')
      const response = {};
      const trailerRental = await trailerRentalService._find({
        query: {
          ownerId: user.company
        }
      })
      trailerRental.data.map((item: any) => {
        response[item.trailerNumber] = 'listing'
      })
      const result = await service._find(params)
      // show only status and trailer number
      result.data.map((item: any) => {
        response[item.trailerNumber] = item.status
      })
      res.json(response)
    } catch (error: any) {
      console.error(`Error get statuses trailer:`, {
        error: error.message,
        params
      })
      Response.error(res, error, error.code)
    }
  })

  // Initialize our service with any options it requires
  app.use('/rental', new Rental(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('rental')
  service.hooks(hooks)
}
