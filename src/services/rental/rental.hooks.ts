import { HookContext } from '@feathersjs/feathers'
import { BadRequest } from '@feathersjs/errors'
import { CTVRental } from '../../models/rentals.model'

export default {
  before: {
    all: [
      async (context: HookContext) => {
        // console.log(
        //   '/-----------/-----------/-----------/-----------/-----------/-----------/-----------'
        // )
        // console.log('header', context.params)
      }
    ],
    find: [async (context: any) => {
      const query = context.params.query
      if (query.trailerNo) {
        query.trailerNumber = { $regex: query.trailerNo, $options: 'i' }
      }
      if (query.trailerNumber) {
        query.trailerNumber = { $regex: query.trailerNumber, $options: 'i' }
      }
      if (query['rentingDetails.dailyPrice']) {
        query['rentingDetails.dailyPrice'] = +query['rentingDetails.dailyPrice']
      }
      context.query = query
      return context
    }],
    get: [],
    create: [
      async function isAvailableForNewRental(context: any) {
        const { data } = context
        const { trailerRentalId } = data
        const rental = context.app.service('rental').Model.find({
          trailerRentalId,
          status: {$in: CTVRental.getProcessingStatus()}
        }).lean()
        if (rental.length > 0) {
          throw new BadRequest('Trailer is leased')
        }
      }
    ],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [
      async (context: HookContext) => {
        // Clean the result object to prevent circular reference issues in feathers-sync
        // This is especially important for the sendSharingNotification hook
        console.log(`[RENTAL HOOKS] Cleaning result object for feathers-sync compatibility`)

        if (context.result && typeof context.result === 'object') {
          // Create a clean version of the result without circular references
          const cleanResult = {
            _id: context.result._id,
            status: context.result.status,
            trailerNumber: context.result.trailerNumber,
            trailerRentalId: context.result.trailerRentalId,
            trailerId: context.result.trailerId,
            start: context.result.start,
            end: context.result.end,
            // Clean owner object
            owner: context.result.owner ? {
              _id: context.result.owner._id,
              name: context.result.owner.name,
              email: context.result.owner.email,
              company: context.result.owner.company
            } : null,
            // Clean rentee object
            rentee: context.result.rentee ? {
              _id: context.result.rentee._id,
              name: context.result.rentee.name,
              email: context.result.rentee.email,
              company: context.result.rentee.company
            } : null,
            // Clean assignee object
            assignee: context.result.assignee ? {
              _id: context.result.assignee._id,
              name: context.result.assignee.name,
              email: context.result.assignee.email
            } : null,
            createdAt: context.result.createdAt,
            updatedAt: context.result.updatedAt
          }

          console.log(`[RENTAL HOOKS] Result object cleaned successfully`)
          context.result = cleanResult
        }

        return context
      }
    ],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
