import { HooksObject, HookContext } from '@feathersjs/feathers'
import { BadRequest } from '@feathersjs/errors'
import { CTVRental } from '../../models/rentals.model'

export default {
  before: {
    all: [
      async (context: HookContext) => {
        // console.log(
        //   '/-----------/-----------/-----------/-----------/-----------/-----------/-----------'
        // )
        // console.log('header', context.params)
      }
    ],
    find: [async (context: any) => {
      const query = context.params.query
      if (query.trailerNo) {
        query.trailerNumber = { $regex: query.trailerNo, $options: 'i' }
      }
      if (query.trailerNumber) {
        query.trailerNumber = { $regex: query.trailerNumber, $options: 'i' }
      }
      if (query['rentingDetails.dailyPrice']) {
        query['rentingDetails.dailyPrice'] = +query['rentingDetails.dailyPrice']
      }
      context.query = query
      return context
    }],
    get: [],
    create: [
      async function isAvailableForNewRental(context: any) {
        const { data } = context
        const { trailerRentalId } = data
        const rental = context.app.service('rental').Model.find({
          trailerRentalId,
          status: {$in: CTVRental.getProcessingStatus()}
        }).lean()
        if (rental.length > 0) {
          throw new BadRequest('Trailer is leased')
        }
      }
    ],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
