import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { sendErrorToWebhook } from '../../helper/helper'
import { CTVRental } from '../../models/rentals.model'
import * as helper from '../../helper/helper'
import { BadRequest } from '@feathersjs/errors'
import { ERROR_MESSAGE } from '../../configs/constants'
import { IPatchRental, IRental } from '../../interface/rental'
import createApplication, { Id } from '@feathersjs/feathers'
import _ from 'lodash'
import Redis from '../../library/redis'


export class Rental extends Service<IRental> {
  app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async findLocation(arrTrailer: any[]): Promise<any> {
    const redis = new Redis()
    const resultInRedis =
      (await redis.client.get('trailer-monitor-all-response')) || null
    const arrLocationName = resultInRedis
      ? _.map(JSON.parse(resultInRedis.toString())?.data, (tr) => {
          return {
            trailerNumber: tr.trailerNumber.trailerNumber,
            lastLocation: tr.lastLocation,
            locationName: tr.locationName
          }
        })
      : []
    return _.map(arrTrailer, (trailer1) => {
      const obj2 = _.find(arrLocationName, {
        trailerNumber: trailer1.trailerNumber
      })
      return _.merge(trailer1, {
        locationName: obj2?.locationName || 'NO_LOCATION',
        lastLocation: obj2?.lastLocation || null
      })
    })  
  }

  async find(params?: any) {
    const { query, user } = params
    // const { trailerNumber } = query
    // if (trailerNumber) {
    //   const trailerForRent = await this.app.service('trailer-rental-availability').Model.findOne({ 'trailer.trailerNumber': trailerNumber })
    //   query.trailerRentalId = trailerForRent?._id
    // }
    if (user.company) {
      params = {
        ...params,
        query: {
          ...query,
          $or: [
            { 'owner.company': user.company },
            { 'rentee.company': user.company }
          ]
        }
      }
    }

    const result = await super.find(params)
    if ('data' in result) {
      result.data = await this.findLocation(result.data)
    }
    return result
  }

  async _find(params?: createApplication.Params): Promise<createApplication.Paginated<IRental>> {
    // @ts-ignore
    return super._find(params)
  }

  async get(id: string, params?: any) {
    const rental = await super.get(id, { ...params, query: { $populate: 'trailerRentalId' } })
    rental.inspections = await this.app.service('inspection').findInspectionForRental(id, true)
    return rental
  }

  async create(data: any, params?: any) {
    const result = await super.create(data, params)
    const rentals = Array.isArray(result) ? result : [result]
    rentals.map(async rental => {
      if (rental.status == CTVRental.status.Ready) {
        await this.doOnRentalReady(rental)
      }
    })
    return result
  }

  async _patch(id: Id, data: IPatchRental, params?: any, original?: IPatchRental) {
    console.log(`[RENTAL _PATCH] Starting low-level patch for ID: ${id}`)
    console.log(`[RENTAL _PATCH] Data to update:`, data)

    original = original ? _.pick(original, Object.keys(data)) : await super._get(id, { query: { $select: Object.keys(data) } }) as IPatchRental

    console.log(`[RENTAL _PATCH] Original data for history:`, original)

    const updateData: any = {
      $set: data,
      $push: {
        history: { original, updated: data, updatedBy: params.user._id }
      }
    }

    console.log(`[RENTAL _PATCH] MongoDB update operation:`, {
      $set: data,
      historyEntry: { original, updated: data, updatedBy: params.user._id }
    })

    const result = await super._patch(id, updateData)
    console.log(`[RENTAL _PATCH] Low-level patch completed successfully`)

    return result
  }

  async patch(id: Id, data: IPatchRental, params?: any) {
    return this.internalPatch(id, data, params);
  }

  async internalPatch(id: Id, data: IPatchRental, params?: any) {
    console.log(`[RENTAL PATCH] Starting patch for rental ID: ${id}`)
    console.log(`[RENTAL PATCH] Patch data:`, {
      status: data.status,
      dataKeys: Object.keys(data),
      dataValues: data
    })
    console.log(`[RENTAL PATCH] User context:`, {
      userId: params?.user?._id,
      userCompany: params?.user?.company
    })

    try {
      console.log(`[RENTAL PATCH] Fetching original rental data`)
      const original = await super._get(id, { query: { $populate: 'requestId' } })

      console.log(`[RENTAL PATCH] Original rental:`, {
        id: original._id,
        status: original.status,
        trailerNumber: original.trailerNumber,
        ownerCompany: original.owner?.company,
        renteeCompany: original.rentee?.company
      })

      // if (data.start && data.end) {
      //   if (![CTVRental.status.NotStarted, CTVRental.status.UpComing].includes(original.status)) throw new BadRequest(ERROR_MESSAGE.UNABLE_TO_CHANGE_RENTAL_PERIOD)
      //   data.status = CTVRental.status.PendingConfirmationByRentee
      //
      //   const { _id, createdAt, updatedAt, createdBy, updatedBy, ...newRequest } = original.requestId as IRentalRequest
      //   const changeRequest: ICreateRentalRequest = {
      //     ...newRequest,
      //     requestType: CTVRentalRequest.type.ChangeRequest,
      //     status: CTVRentalRequest.status.Pending,
      //     requestStart: data.start,
      //     requestEnd: data.end,
      //     rentalId: id
      //   }
      //   data.changeRequest = await this.app.service('rental-request').create(changeRequest, params) as IRentalRequest
      //   return await this._patch(id, data, params, original)
      // }
      delete data.start
      delete data.end

      const updateToStatus = data.status ? data.status : ''
      console.log(`[RENTAL PATCH] Status update: ${original.status} -> ${updateToStatus}`)

      if (updateToStatus) {
        console.log(`[RENTAL PATCH] Validating status update to: ${updateToStatus}`)
        console.log(`[RENTAL PATCH] Available statuses:`, Object.values(CTVRental.status))

        if (!Object.values(CTVRental.status).includes(updateToStatus)) {
          console.error(`[RENTAL PATCH] Invalid status: ${updateToStatus}`)
          throw new BadRequest(ERROR_MESSAGE.INVALID_STATUS)
        }

        // if (CTVRental.statusTransitions[original.status] && !CTVRental.statusTransitions[original.status].includes(updateToStatus))
        //   throw new BadRequest(ERROR_MESSAGE.UNABLE_TO_CHANGE_RENTAL_STATUS)

        console.log(`[RENTAL PATCH] Checking permissions for status: ${updateToStatus}`)
        console.log(`[RENTAL PATCH] Status permissions:`, CTVRental.statusPermission[updateToStatus])

        let isAllowUpdate = false
        CTVRental.statusPermission[updateToStatus].map(role => {
          const { [role as keyof typeof original]: { company } } = original
          console.log(`[RENTAL PATCH] Checking role: ${role}, company: ${company}, user company: ${params.user.company}`)
          if (!params.user.company || company.toString() === params.user.company) {
            isAllowUpdate = true
            console.log(`[RENTAL PATCH] Permission granted for role: ${role}`)
          }
        })

        if (!isAllowUpdate) {
          console.error(`[RENTAL PATCH] Permission denied for status update`)
          throw new BadRequest(ERROR_MESSAGE.PERMISSION_DENIED)
        }

        console.log(`[RENTAL PATCH] Permission check passed`)
      }


      console.log(`[RENTAL PATCH] Executing database update`)
      const result = await this._patch(id, data, params, original as IPatchRental)

      console.log(`[RENTAL PATCH] Database update completed:`, {
        id: result._id,
        newStatus: result.status,
        originalStatus: original.status
      })

      if (original.status !== result.status) {
        console.log(`[RENTAL PATCH] Status changed, executing post-update actions`)

        if (result.status == CTVRental.status.Ready) {
          console.log(`[RENTAL PATCH] Executing doOnRentalReady`)
          await this.doOnRentalReady(result)
        }
        if (CTVRental.isEndStatus(result.status)) {
          console.log(`[RENTAL PATCH] Executing doOnRentalEnd`)
          await this.doOnRentalEnd(result)
        }
      }

      if (original.assignee?._id !== result.assignee?._id) {
        console.log(`[RENTAL PATCH] Assignee changed, sending notification`)
        await this.sendNotificationToAssignee({ rental: result })
      }

      console.log(`[RENTAL PATCH] Patch operation completed successfully`)
      return result
    } catch (error: any) {
      console.error(`[RENTAL PATCH] Error during patch operation:`, {
        message: error.message,
        stack: error.stack,
        rentalId: id,
        patchData: data
      })
      await sendErrorToWebhook('Error on Patch rental', { data, error: error.message })
      throw error
    }
  }

  async doOnRentalEnd(rental: IRental) {
    try {

      switch (rental.status) {
        case CTVRental.status.Canceled: {
          // await this.sendNotificationToCancelRental({ rental })
          // send notification is handled in middleware notification
          break;
        }
        case CTVRental.status.Completed: {
          const inspections = await this.app
            .service('inspection')
            .findInspectionForRental(rental._id + '', false)
          console.log('inspections: ' + JSON.stringify(inspections, null, 2))
          await this.app.service('billings')._create({
            rental,
            inspections,
            trailerRental: rental.trailerRentalId
          })
          break;
        }
        default: break;
      }
      // Fresh trailer-rental-availability
      await this.app.service('trailer-rental-availability')._patch(String(rental.trailerRentalId), {
        isLeasing: false
      })

      // Fresh trailer-master
      // @ts-ignore
      // await this.app.service('trailers').update(String(rental.trailerId), {leasee: null, onLease: false}, {token: await this.app.getCDASToken()});
      let requestUpdate = {
        method: 'PATCH',
        url: `${this.app.get('CTRUrl')}/trailer-master/${rental.trailerId}`,
        token: await this.app.getCDASToken(),
        body: { leasee: null, onLease: false }
      }
      await sendErrorToWebhook('requestUpdate', requestUpdate)
      const result = await helper.request(requestUpdate)
    } catch (error: any) {
      await sendErrorToWebhook('Error on end of Rental', { rental, error: error.message })
      // throw error
    }
  }

  async doOnRentalReady(rental: IRental) {
    try {
      if (rental.assignee?._id) {
        await this.sendNotificationToAssignee({
          rental,
          content: `Trailer ${rental.trailerNumber} is ready to pickup`
        })
      }
    } catch (error: any) {
      await sendErrorToWebhook('Error on rental ready', { rental, error: error.message })
      throw error
    }
  }

  async sendNotificationToAssignee({ rental, content }: any) {
    const { assignee, rentee } = rental
    const sendData = {
      type: 'push',
      typeStorage: 'Assign driver',
      subject: 'Trailer assigned: ' + rental.trailerNumber,
      receiver: assignee?._id?.toString(),
      companyId: rentee.company._id,
      content: content ? content : `The trailer ${rental.trailerNumber} is assigned to you`
    }
    console.log('sendData', sendData)
    try {
      // const sender = (app.get('env') == 'production') ? '<EMAIL>' : '<EMAIL>'
      // @ts-ignore
      await this.app.sendCustomNotification(sendData)
    } catch (error: any) {
      console.log('LOG-error', error);
      await sendErrorToWebhook('Error Send Notification To Assignee', { sendData, error: error.message })
      throw error
    }
  }

  // async sendNotificationToCancelRental({ rental, content }: any) {
  //   const { rentee } = rental
  //   try {
  //     // const sender = (app.get('env') == 'production') ? '<EMAIL>' : '<EMAIL>'
  //     // @ts-ignore
  //     await this.app.sendCustomNotification({
  //       type: 'email',
  //       typeStorage: 'rental canceled',
  //       subject: 'Trailer rental was canceled: ' + rental.trailerNumber,
  //       receiver: rentee?.email,
  //       companyId: rentee?.company,
  //       content: content ? content : `The rental of trailer  ${rental.trailerNumber} is canceled by Owner company`
  //     })
  //   } catch (error: any) {
  //     await sendErrorToWebhook('Error sendNotificationToCancelRental', { rental, error: error.message })
  //     throw error
  //   }
  // }

  async getProcessingRental(TRAId: string, startTime: any, endTime: any, _id?: string) {
    const query: any = {
      trailerRentalId: TRAId,
      $or: [
        {
          start: { $lte: startTime },
          end: { $gte: startTime }
        },
        {
          start: { $lte: endTime },
          end: { $gte: endTime }
        },
        {
          start: { $gte: startTime },
          end: { $lte: endTime }
        }
      ],
      status: { $in: CTVRental.getProcessingStatus() }
    }
    if (_id) query._id = { $ne: _id }
    return this.Model.findOne(query).lean()
  }

  async getUpComingRental(TRAId: string, renteeCompanyId?: string) {
    const query: any = {
      trailerRentalId: TRAId,
      status: CTVRental.status.UpComing
    }
    if (renteeCompanyId) query['rentee.company'] = renteeCompanyId
    return this.Model.find(query).lean()
  }

  async getActiveRental(TRAId: Id, renteeCompanyId?: string) {
    const queryRental: any = {
      trailerRentalId: TRAId,
      status: { $in: CTVRental.getProcessingStatus() }
    }
    const select = {
      'start': 1, 'end': 1, 'status': 1
    }
    if (renteeCompanyId) queryRental['rentee.company'] = renteeCompanyId
    const result = await this.Model.find(queryRental, select).lean() as any[]
    // const queryRequest: any = {
    //   trailerRentalId: TRAId,
    //   status: CTVRentalRequest.status.Pending,
    //   requestType: CTVRentalRequest.type.ChangeRequest
    // }
    // const requestResult = await this.app.service('rental-request').Model.find(queryRequest, {
    //   requestStart: 1,
    //   requestEnd: 1,
    //   status: 1,
    //   requestType: 1
    // })
    // result.push(...requestResult.map((rq: any) => {
    //   return {
    //     start: rq.requestStart,
    //     end: rq.requestEnd,
    //     status: rq.status
    //   }
    // }))
    return result
  }

  async singleCreate(data: Partial<IRental>, params?: createApplication.Params): Promise<IRental> {
    // @ts-ignore
    return super._create(data, params)
  }

}
