import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import _ from 'lodash'
import { sendErrorToWebhook } from '../../helper/helper'
import { ICreateTrailerSharing, ITrailerSharing } from '../../interface/trailer-sharing'

export class TrailerSharing extends Service<ITrailerSharing> {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async getTrailerNosIsShared(company: string, params?: any): Promise<string[]> {
    const trailerNos: string[] = []
    const filter: any = {
      sharedCompany: { '$in': [company] },
      isAvailable: true
    }
    if (params?.user?.company) {
      filter.companyId = params.user.company
    }
    const sharing = await this.Model.find(filter).lean()

    const today = new Date()
    sharing.map((s: any) => {
      const listTrA = _.filter(s.trailers, 'isEnable')
      trailerNos.push(..._.map(listTrA, 'trailerNo'))
    })

    return trailerNos
  }

  async create(data: ICreateTrailerSharing, params?: any) {
    const { trailers: listTrailerToShare, sharedCompanies, companyId } = data
    const trailerNos = _.map(listTrailerToShare, 'trailerNo')

    const listTRA = await this.app.service('trailer-rental-availability').Model.find({
      trailerNumber: { $in: trailerNos },
      deletedAt: null
    }).lean()

    const trailers = listTrailerToShare.map((tr: any) => {
      const found: any = listTRA.find((a: any) => {
        return a.trailerNumber == tr.trailerNo
      })
      if (found)
        tr.rentingDetails = found.rentingDetails
      return tr
    })
    const createData: any = []
    const result: any = []
    const trailerSharing = await this.Model.find({ companyId, shareTo: { $in: sharedCompanies } }).lean()
    for (const shareCpn of sharedCompanies) {
      const sharedCpn: any = trailerSharing.find((s: any) => s.sharedCompany.toString() == shareCpn.toString())
      if (sharedCpn) {
        trailers.map((newTr: any) => {
          const indexShared = sharedCpn.trailers.find((trailer: any) => trailer.trailerNo.toString() === newTr.trailerNo.toString())
          if (!indexShared) sharedCpn.trailers.push(newTr)
        })
        sharedCpn.trailerNos = _.map(sharedCpn.trailers, 'trailerNo')
        sharedCpn.trailerIds = _.map(sharedCpn.trailers, '_id')
        const update = await super.patch(sharedCpn._id, sharedCpn)
        result.push(update)
      } else {
        const newSharing: ITrailerSharing = {
          sharedCompany: shareCpn,
          trailers,
          trailerNos: _.map(trailers, 'trailerNo'),
          trailerIds: _.map(trailers, '_id'),
          createdBy: params.user._id,
          updatedBy: params.user._id,
          isAvailable: true,
          companyId
        }
        createData.push(newSharing)
      }
    }

    let createResult = await super.create(createData, params)
    createResult = Array.isArray(createResult) ? createResult : [createResult]
    result.push(...createResult)
    return result
  }

  async patch(id: string, data: any, params?: any){
    if (data.trailers) {
      data.trailerNos = _.map(data.trailers, 'trailerNo')
      data.trailerIds = _.map(data.trailers, '_id')
    }
    data.updatedBy = params.user._id
    if (params.user.company) data.companyId = params.user.company
    return super.patch(id, data, params)
  }
}
