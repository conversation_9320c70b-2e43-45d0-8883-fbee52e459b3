// Initializes the `trailer-sharing` service on path `/trailer-sharing`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { TrailerSharing } from './trailer-sharing.class'
import createModel from '../../models/trailer-sharing.model'
import hooks from './trailer-sharing.hooks'
import authAPI from '../../middleware/api-auth'
import _ from 'lodash'
import * as helper from '../../helper/helper'
import APIisShare from '../../middleware/api-is-share'
import moment from 'moment'
import { sendErrorToWebhook } from '../../helper/helper'
import { isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'
import { NotFound } from '@feathersjs/errors'
import { DEFAULT_PAGING } from '../../configs/constants'
import * as Response from '../../helper/response'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-sharing': TrailerSharing & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
    multi: ['create', 'remove'],
    lean: true
  }

  app.post('/company/sharing-status', async (req: any, res: any) => {
    try {
      let companyId = req.body.companyId
      let data = {
        method: 'PATCH',
        url: `${app.get('CTRUrl')}/groups/${companyId}`,
        token: req.headers.authorization,
        body: { trailerShareStatus: req.body.trailerShareStatus }
      }
      let result: any = await helper.request(data)
      Response.success(res, result.data, 200)
    } catch (error: any) {
      Response.error(res, { error: error.message }, error.code)
    }
  })

  app.post(
    '/trailer-sharing/list-available',
    authAPI,
    async (req: any, res: any) => {
      try {
        const { arrShareTo } = req.body
        if (!arrShareTo || !Array.isArray(arrShareTo))
          res.status(422).json({ message: 'arrShareTo is required (array type)' })

        const companyId = req.user.company ? req.user.company : req.body.companyId
        if (!companyId) res.status(422).json({ message: 'companyId is required' })

        const { $limit = DEFAULT_PAGING.LIMIT, $skip = DEFAULT_PAGING.SKIP } = req.query
        let condition: string = `&company=${companyId}&$limit=${$limit}&$skip=${$skip}`

        if (arrShareTo.length) {
          const listTS = await app.service('trailer-sharing').Model.find({
            sharedCompany: { $in: arrShareTo },
            companyId
          }).lean()

          condition += _.uniq(_.flatMap(listTS, 'trailerIds')).map((num) => `&_id[$nin][]=${num}`).join('');
        }

        const filter = _.pickBy(req.query, (value, key) => !['companyId', '$limit', '$skip'].includes(key))
        for (const key in filter) {
          condition += `&${key}=${filter[`${key}`]}`
        }

        let data = {
          method: 'GET',
          url: `${app.get('CTRUrl')}/trailer-master?deleted=false&${condition}`,
          token: req.headers.authorization
        }
        let trailerMaster: any = await helper.request(data)
        const trailerNos = _.map(trailerMaster.data.data, 'trailerNumber')
        const tras = await app.service('trailer-rental-availability').Model.find({
          trailerNumber: { $in: trailerNos },
          deletedAt: null,
        }).lean()
        res.json({
          ...trailerMaster.data,
          data: trailerMaster.data.data.map((trailer: any) => {
            const a: any = tras.find((tra: any) => tra.trailerNumber == trailer.trailerNumber)
            trailer.rentingDetails = a?.rentingDetails
            return trailer
          })
        })
      } catch (error: any) {
        console.error('/trailer-sharing/list-available error', error)
        Response.error(res, { error: error.message }, error.code)
      }
    }
  )

  app.patch('/trailer-sharing/:id/trailer/:trailerNo',
    authAPI, async (req: any, res: any) => {
      const { id, trailerNo } = req.params
      try {
        const trailerSharing = await service._get(id)
        const trailers = trailerSharing.trailers.map((trailer: any) => {
          if (trailer.trailerNo == trailerNo) {
            return { ...trailer, ...req.body }
          }
          return trailer
        })
        const result = await service.patch(id, { trailers }, req)
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error('/trailer-sharing/:id/trailer/:trailerNo error', error)
        await sendErrorToWebhook('/trailer-sharing/:id/trailer/:trailerNo error', error)
        Response.error(res, { error: error.message }, error.code)
      }
    })


  app.delete('/trailer-sharing/:id/trailer/:trailerNo',
    authAPI, async (req: any, res: any) => {
      const { id, trailerNo } = req.params
      try {
        const trailerSharing = await service._get(id)
        const trailers = trailerSharing.trailers
          .map((trailer: any) => {
            if (trailer.trailerNo !== trailerNo) return trailer
          })
          .filter((trailer: any) => trailer !== undefined)

        const result = await service.patch(id, { trailers }, req)
        Response.success(res, result, 200)
      } catch (error: any) {
        console.error('DELETE /trailer-sharing/:id/trailer/:trailerNo error', error)
        await sendErrorToWebhook('/trailer-sharing/:id/trailer/:trailerNo error', error)
        Response.error(res, { error: error.message }, error.code)
      }
    })


  app.delete('/trailer-sharing', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    try {
      const ids = req.query.ids
      const result =await Promise.all(ids.map(async (id: string) => {
        return service.remove(id, req)
      }))
      Response.success(res, result, 200)
    } catch (error: any) {
      await sendErrorToWebhook('Error delete trailer-sharing', { error: error.message })
      Response.error(res, { error: error.message }, error.code)
    }
  })

  // Initialize our service with any options it requires
  app.use('/trailer-sharing', new TrailerSharing(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-sharing')
  service.hooks(hooks)
}


