import { HookContext, HooksObject } from '@feathersjs/feathers'
import auth from '../../middleware/auth'
import moment from 'moment'
import * as errors from '@feathersjs/errors'
import * as helper from '../../helper/helper'
import { sendErrorToWebhook } from '../../helper/helper'
import _ from 'lodash'
import { appendFilterAuditBy } from '../../middleware/hook-filter'

async function syncAvailableForRentToInTrailerRental(context: any) {
  const { result } = context
  const sync = async (trailerNos: any) => {
    try {
      const shareParts = await context.app.service('trailer-sharing').Model.find({
        trailerNos: { $in: trailerNos },
        isAvailable: true
      }).lean()
      trailerNos.map(async (trNo: string) => {
        const availableTo: any = []
        shareParts.map((shareP: any) => {
          shareP.trailers
            .filter((tr: any) => tr.trailerNo == trNo)
            .map((sharedTrailer: any) => availableTo.push({
              trailerSharingId: shareP._id,
              companyId: shareP.sharedCompany,
              availableFrom: sharedTrailer.startDate,
              availableTo: sharedTrailer.endDate,
              isEnable: sharedTrailer.isEnable
            }))

        })
        const updateData = {
          availableForRentTo: _.uniqWith(availableTo, _.isEqual)
        }
        await context.app.service('trailer-rental-availability').Model.updateOne({ 'trailer.trailerNumber': trNo }, updateData)
      })
    } catch (e) {
      await sendErrorToWebhook('syncAvailableForRentToInTrailerRental Error ' + e, {})
      return context
    }
  }
  let trailerNos: string[] = []
  if (result.trailerNos) trailerNos.push(...result.trailerNos)
  if (context.params.trailerNos) trailerNos.push(...context.params.trailerNos)
  if (Array.isArray(result)) {
    trailerNos = _.union(result.map(s => s.trailerNos).flat(), 'trailerNos')
  }

  await sendErrorToWebhook('trailerNos in syncAvailableForRentToInTrailerRental', _.union(trailerNos))
  await sync(_.union(trailerNos))
  return context
}

async function createTrailerAvailability(context: any) {
  try {

    let { trailers } = context.result
    if (Array.isArray(context.result)) {
      trailers = _.unionBy(_.flatMap(_.map(context.result, 'trailers')), 'trailerNo')
    }
    await Promise.all(trailers.map(async (tra: any) => {
      try {
        const { rentingDetails, trailerNo, _id } = tra
        const isExist = await context.app.service('trailer-rental-availability').Model.exists({ 'trailerNumber': trailerNo, 'deletedAt': null })
        if (isExist) return

        console.log('isExist after edit trailer-sharing: ',isExist)
        let data = {
          method: 'GET',
          url: `${context.app.get('CTRUrl')}/trailer-master/${_id}`,
          token: context.params.headers.authorization
        }
        let ctrTrailer = await helper.request(data)
        // if (!ctrTrailer.data) throw new Error(trailerNo + ' Not found in ctr')
        const trailer = ctrTrailer.data
        const newTrAv = {
          trailer,
          isAvailable: true,
          ownerId: trailer?.company?._id,
          rentingDetails
        }
        await context.app.service('trailer-rental-availability')._create(newTrAv, context.params)
      } catch (e) {
        console.error(`Error in create trailerAvailability [${tra.trailerNo}]: `, e)
        await sendErrorToWebhook(`Error in create trailerAvailability [${tra.trailerNo}]` + e, e)
        return context
      }
    }))
  } catch (e) {
    console.error('createTrailerAvailability ERROR: ', e)
    return context
  }

  return context
}

const prepareData = async (context: any) => {
  if (context.data.startDate && context.data.endDate) {
    context.data.startDate = moment(context.data.startDate).toISOString()
    context.data.endDate = moment(context.data.endDate).toISOString()
    if (moment(context.data.startDate).isAfter(context.data.endDate)) throw new errors.Unprocessable('endDate must be after startDate')
  }
  context.data.createdBy = context.params.user._id
  if (!context.params.user.company) {
    context.data.isCreatedByAdmin = true
  } else {
    context.data.companyId = context.params.user.company
  }
  if (context.data.shareTo && context.data.shareTo.length > 0) context.data.isPublicShare = false
  return context
}

export default {
  before: {
    all: [
      async (context: HookContext) => {
        return await auth(context)
      }
    ],
    find: [
      appendFilterAuditBy,
      async (context: any) => {
        if (context.params.user.company) context.params.query.companyId = context.params.user.company
        return context
      }
    ],
    get: [],
    create: [
      async (context: any) => {
        // await isShare(context);
        context.data.createdBy = context.params.user._id
        if (context.params.user.company) {
          context.data.companyId = context.params.user.company
        }
        return context
      }
    ],
    update: [],
    patch: [async (context: any) => {
      const ts = await context.app.service('trailer-sharing')._get(context.id)
      context.params.trailerNos = ts.trailerNos
      return context
    }],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [
      createTrailerAvailability, syncAvailableForRentToInTrailerRental
    ],
    update: [],
    patch: [
      createTrailerAvailability, syncAvailableForRentToInTrailerRental
    ],
    remove: [
      syncAvailableForRentToInTrailerRental
    ]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
