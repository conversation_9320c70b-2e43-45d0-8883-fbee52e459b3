// Initializes the `repair-report-audit-logs` service on path `/repair-report-audit-logs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { RepairReportAuditLogs } from './repair-report-audit-logs.class';
import createModel from '../../models/repair-report-audit-logs.model';
import hooks from './repair-report-audit-logs.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'repair-report-audit-logs': RepairReportAuditLogs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/repair-report-audit-logs', new RepairReportAuditLogs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('repair-report-audit-logs');

  service.hooks(hooks);
}
