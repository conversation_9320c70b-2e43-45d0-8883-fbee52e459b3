// Initializes the `trailer-assign` service on path `/trailer-assign`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerAssign } from './trailer-assign.class';
import createModel from '../../models/trailer-assign.model';
import hooks from './trailer-assign.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-assign': TrailerAssign & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/trailer-assign', new TrailerAssign(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-assign');

  service.hooks(hooks);
}
