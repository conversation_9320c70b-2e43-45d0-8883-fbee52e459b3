import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { JoinRequest } from './join-request.class'
import hooks from './join-request.hooks'
import createModel from '../../models/join-request.model'
import authAPI from '../../middleware/api-auth'
import * as helper from '../../helper/helper'
import moment from 'moment'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'join-request': JoinRequest & ServiceAddons<any>;
  }
}


export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/join-request', new JoinRequest(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('join-request')

  app.get('/tnc', authAPI, async (req: any, res: any) => {
    try {
      const tnc = '' +
        'Payment: The rental payment shall be paid in full on or before the due date as specified in the rental agreement. Any late payment shall incur a penalty fee of [insert amount] per day.\n' +
        '\n' +
        'Security deposit: A security deposit of [insert amount] shall be paid upon signing the rental agreement. This deposit will be refunded at the end of the rental term, provided the property is returned in the same condition as it was received.\n' +
        '\n' +
        'Duration: The rental agreement shall be for a period of [insert duration], starting from [insert start date] and ending on [insert end date].\n' +
        '\n' +
        'Use of property: The rental property shall only be used for residential purposes and shall not be used for any illegal activities.\n' +
        '\n' +
        'Maintenance and repairs: The tenant shall be responsible for the maintenance and repairs of the property, except for those repairs caused by normal wear and tear.\n' +
        '\n' +
        'Occupancy: The rental property shall only be occupied by the tenant and the tenant\'s immediate family members. No subleasing or sharing of the property is allowed.\n' +
        '\n' +
        'Termination: Either party may terminate this agreement by providing [insert notice period] days\' written notice to the other party.\n' +
        '\n' +
        'Default: If the tenant breaches any of the terms of this agreement, the landlord may terminate the agreement immediately and without notice.\n' +
        '\n' +
        'Insurance: The tenant is responsible for insuring their personal property against loss or damage.'
      const version = app.get('tncVersion')
      let statusJoining = 'NotSent'
      if (req.user.company) {
        const requestJoining = await service.Model.find({
            company: req.user.company,
            tncVersion: version,
        }).limit(1).sort({ _id: -1 }).lean();

        if (requestJoining.length) {
          // @ts-ignore
          statusJoining = requestJoining[0].status;
          console.log('requestJoining', requestJoining)
        }
      }
      res.json({ content: tnc, version, statusJoining })
    } catch ({ message }) {
      res.status(500).json({ message })
    }
  })

  service.hooks(hooks)
}
