import { HooksObject, HookContext } from '@feathersjs/feathers'
import { BadRequest } from '@feathersjs/errors'
import { CTVJoinRequest } from '../../models/join-request.model'

export default {
  before: {
    all: [
      async (context: HookContext) => {
        // console.log(
        //   '/-----------/-----------/-----------/-----------/-----------/-----------/-----------'
        // )
        // console.log('header', context.params)
      }
    ],
    find: [(context: any) => {
      const query = context.params.query;
      if (context.params.user.company) {
        query.company = context.params.user.company
      }
      context.query = query;
      return context;
    }],
    get: [],
    create: [async (context: HookContext) => {
      const { data } = context;
      let { company } = data;
      if (!company) {
        company = context?.params?.user?.company;
      }

      const block = await context.app.service('dynamic-blacklist').Model.findOne({company}).lean();
      if (block) throw new BadRequest('CDAS administration restricts your company from sending requests.');

      const joinRequest = await context.app.service('join-request').Model.findOne({
        company,
        status: { $in: [CTVJoinRequest.status.Approved, CTVJoinRequest.status.Pending] }
      }).lean();

      if (joinRequest) {
        const message = joinRequest.status === CTVJoinRequest.status.Approved
          ? 'The previous request of this company was approved. You can access the Dynamic Sharing feature'
          : 'The previous request of this company was submitted, Please wait for review from CDAS';
        throw new BadRequest(message);
      }

      return context;
    }
    ],
    update: [],
    patch: [
      async (context: HookContext) => {
        const { data } = context
        const { status, rejectReason } = data

        // If status is not "Pending", set the review time to the current date
        if (status && status !== CTVJoinRequest.status.Pending) {
          data.reviewTime = new Date()

          // If status is "Rejected" and rejectReason is null or undefined, throw a BadRequest error
          if (status === CTVJoinRequest.status.Rejected && !rejectReason) {
            throw new BadRequest('Please provide a reason for the rejection')
          }
        }

        return context
      }
    ],

    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
