import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { Params } from '@feathersjs/feathers'

export class JoinRequest extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app;
  }

  async create(data: any, param: any) {
    if (param?.user?.company) {
      data.company = param?.user?.company
    }
    return await super.create(data, param)
  }

  async patch(id: string, data: Partial<any>, params?: any): Promise<any[] | any> {
    if (data.isPrevent) await this.app.service('dynamic-blacklist').create({company: data.company});
    return super.patch(id, data, params)
  }

}
