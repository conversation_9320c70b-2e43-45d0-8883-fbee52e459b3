import { Service, MongooseServiceOptions } from 'feathers-mongoose';
import { Application } from '../../declarations';
import {getQueryConditionHistoryTrailer} from "../../helper/helper";

export class TrailerAllocation extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: any;
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options);
    this.app = app;
  }

  async create(data:object) {
    try {
      return await super.Model.create(data);
    }catch (e:any) {
      throw e;
    }
  }

  async find(param:any) {
    try {
      let query = getQueryConditionHistoryTrailer(param)
      if (param.user.company) query.company = param.user.company
      return await super.find({query})
    } catch (e: any) {
      throw e;
    }
  }
}
