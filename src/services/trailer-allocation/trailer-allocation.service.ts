// Initializes the `trailer-allocation` service on path `/trailer-allocation`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerAllocation } from './trailer-allocation.class';
import createModel from '../../models/trailer-allocation.model';
import hooks from './trailer-allocation.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-allocation': TrailerAllocation & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/trailer-allocation', new TrailerAllocation(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-allocation');

  service.hooks(hooks);
}
