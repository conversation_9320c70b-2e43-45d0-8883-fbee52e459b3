// Initializes the `trailer-sharing` service on path `/trailer-sharing`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Billing } from './billings.class'
import createModel from '../../models/billings.model'
import hooks from './billings.hooks'
import authAPI from '../../middleware/api-auth'
import * as helper from '../../helper/helper'
import { TrailerMasterCTR } from '../../interface/etrailer'
import { UserCTR } from '../../interface/user'
import moment from 'moment'
import xlsx from 'json-as-xlsx'
import S3AWS from '../../library/s3AWS'
import { IBilling } from '../../interface/billing'
import { isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'
import * as Response from '../../helper/response'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'billings': Billing & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$populate', '$options', '$push', '$set'],
    multi: ['remove', 'patch']
  }

  const s3 = new S3AWS()

  app.get('/billings/lease-out', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    const { query, user } = req
    const params = {
      query
    }
    const company = user.company || query?.company
    if (company) {
      delete query?.company
      params.query = {
        ...query,
        'rental.owner.company': company
      }
    }
    try {
      const result = await service._find(params)
      res.json(result)
    } catch (error: any) {
      console.error(`Error while retrieving lease-out billings: ${error.message}`)
      console.error(error.stack)
      Response.error(res, { error: 'An error occurred while processing your request' }, 500)
    }
  })

  app.get('/billings/lease-in', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    const { query, user } = req
    const params = {
      query
    }
    const company = user.company || query?.company
    if (company) {
      delete query?.company
      params.query = {
        ...query,
        'rental.rentee.company': company
      }
    }
    try {
      const result = await service._find(params)
      res.json(result)
    } catch (error: any) {
      console.error(`Error while retrieving lease-in billings: ${error.message}`)
      console.error(error.stack)
      Response.error(res, { error: 'An error occurred while processing your request' }, 500)
    }
  })

  // Initialize our service with any options it requires
  app.use('/billings', new Billing(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('billings')

  app.get('/billings-export-xls', authAPI, async (req: any, res: any) => {
    try {
      let getListUserRequest = {
        method: 'GET',
        url: `${app.get('CTRUrl')}/usersRestricted?$limit=1000`,
        token: req.headers.authorization
      }
      let responseUser = await helper.request(getListUserRequest)

      const billings: any = await service._find({ query: req.query })
      let arrSheet: any[] = []
      await Promise.all(billings.data.map(async (item: any) => {

        let updatedBy = ''
        if (item.updatedBy) {
          const userCreated = responseUser.data.data.find((element: UserCTR) => element._id == item.updatedBy)
          updatedBy = userCreated ? userCreated.username : ''
        }
        let row = {
          trailerNo: item.rental.trailerNumber,
          renteeCompany: item.rental.rentee.name,
          ownerCompany: item.rental.owner.name,
          status: item.status,
          daily: item.rental.rentingDetails.dailyPrice,
          days: item.rental.numberOfDays,
          total: item.rental.estimatedAmount,
          startDate: moment(item.rental.startDate).format('DD-MM-YYYY'),
          endDate: moment(item.rental.endDate).format('DD-MM-YYYY'),
          updatedDate: item.updatedAt ? moment(item.updatedAt).format('DD-MM-YYYY') : null,
          createdDate: item.createdAt ? moment(item.createdAt).format('DD-MM-YYYY') : null,
          updatedBy
        }
        arrSheet.push(row)
      }))
      const data: any[] = [
        {
          sheet: 'Billing',
          columns: [
            { label: 'Plate #', value: 'trailerNo' },
            { label: 'Owner Company', value: 'ownerCompany' },
            { label: 'Rentee Company', value: 'renteeCompany' },
            { label: 'Created Date', value: 'createdDate' },
            { label: 'Updated By', value: 'updatedBy' },
            { label: 'Edited Date', value: 'updatedDate' },
            { label: 'Start Date', value: 'startDate' },
            { label: 'End Date', value: 'endDate' },
            { label: 'Daily', value: 'daily' },
            { label: 'Days', value: 'days' },
            { label: 'Total $', value: 'total' },
            { label: 'Status', value: 'status' }
          ],
          content: arrSheet
        }
      ]

      const settings: any = {
        writeOptions: {
          type: 'buffer',
          bookType: 'xlsx'
        }
      }
      const buffer = xlsx(data, settings)
      const now = Math.round(+new Date() / 1000)
      let url = await s3.upload(buffer, `${now}_billing${moment(new Date()).format('DD-MM-YYYY')}.xlsx`)
      res.json({ url })
    } catch (error: any) {
      if (error?.response?.data)
        res.status(500).json(error.response.data)
      res.status(500).json(error.message)
    }

  })

  service.hooks(hooks)
}
