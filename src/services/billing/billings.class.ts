import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { Conflict, NotFound } from '@feathersjs/errors'
import { CTVRental } from '../../models/rentals.model'
import { IPatchBilling, IBilling } from '../../interface/billing'
import createApplication from '@feathersjs/feathers'
import { ITrailerRentalAvailability } from '../../interface/trailer-rental-availability'
import _ from 'lodash'
import { IPatchRental } from '../../interface/rental'
import S3AWS from '@src/library/s3AWS'

export class Billing extends Service<IBilling> {
  private app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async _find(params?: any) : Promise<createApplication.Paginated<IBilling>> {
    // @ts-ignore
    return super._find({
      query: {
        ...params.query,
        $populate: ['trailerRental', 'rental'],
      }
    })
  }

  async get(id: string, params?: any) {
    const billing = await super.get(id, {
      ...params,
      query: { $populate: ['trailerRental', 'rental'] }
    })

    const s3 = new S3AWS()
    await Promise.all(
      // @ts-ignore
      billing.inspections.map(async (inspection: IInspection) => {
        inspection.images = await Promise.all(
          inspection.images.map(
            (image) => s3.getUrl(image, 5 * 60) as Promise<string>
          )
        )
        return inspection
      })
    )
    return billing
  }

  async create(data: IPatchBilling, params?: any) {
    return super.create(data, params)
  }
}
