import { HookContext } from "@feathersjs/feathers"

const removeUserFromAnotherRole = async (context: HookContext) => {

  let { app, result } = context
  
  console.log('LOG-result',result);
  result = Array.isArray(result) ? result : [result]
  await Promise.all(result.map(async (role: any) => {
    const { _id, users, company } = role
    await Promise.all(users.map(async (user: any) => {
      // remove this user in list users of another company-role
      await app.service('company-roles').Model.updateMany({
        company,
        _id: { $ne: _id }
      }, {
        $pull: { users: user }
      })
    }))
  }))
}

const removeCpnRoleInUser = async (context: HookContext) => {
  const { app, result } = context
  const { users } = result
  // remove cpnRole for all users in this role 
  await app.service('users').Model.updateMany(
    { _id: {$in: users} },
    { cpnRole: null })
}
export default {
  before: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [removeUserFromAnotherRole],
    update: [removeUserFromAnotherRole],
    patch: [removeUserFromAnotherRole],
    remove: [removeCpnRoleInUser]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
