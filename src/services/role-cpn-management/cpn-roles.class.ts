import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { ICreateRole, IRole } from '../../interface/role'
import createApplication from '@feathersjs/feathers'
import * as helper from '../../helper/helper'

export class ETSRole extends Service<IRole> {
  private app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async _create(
    data: ICreateRole,
    params?: createApplication.Params
  ) {
    let roles = Array.isArray(data) ? data : [data]
    roles = await Promise.all(roles.map(async (role) => {
      const users = role.users;
      role.company = params?.user.company ? params?.user.company : role.company
      // create user if not exist in db
      role.users = await Promise.all(users.map(async (user) => {
        const userExist = await this.app.service('users').Model.findOne({_id: user})
        if (!userExist) {
          let data = {
            method: 'GET',
            url: `${this.app.get('CTRUrl')}/users/${user}`,
            token: await this.app.getCDASToken()
          }
          let result = await helper.request(data)
          user = (await this.app.service('users').Model.create(result.data))._id
        }
        return user;
      }))
      return role;
    }))
    const result: any = await super._create(roles, params)

    await Promise.all(result.map((role: any) => {
      role.users.map(async (user) => {
        await this.app.service('users').Model.findByIdAndUpdate(user, {cpnRole: role._id})
      })
    }))
    return result
  }

  async _find(params: any) {
    // add query for company
    if (params.user?.company) {
      params.query.company = params.user.company
    }
    const result = await super._find(params)
    return result
  }

}
