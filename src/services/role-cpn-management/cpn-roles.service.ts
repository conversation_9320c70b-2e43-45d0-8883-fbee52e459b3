// Initializes the `trailer-sharing` service on path `/trailer-sharing`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { ETSRole } from './cpn-roles.class'
import createModel from '../../models/cpn-roles.model'
import hooks from './cpn-roles.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'company-roles': ETSRole & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$populate', '$options'],
    multi: ['remove', 'patch']
  }

  // Initialize our service with any options it requires
  app.use('/company-roles', new ETSRole(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('company-roles')

  // service.publish((data, context) => {
  //   return app.channel('public')
  // })
  service.hooks(hooks)
}
