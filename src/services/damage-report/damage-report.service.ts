// Initializes the `damage-report` service on path `/damage-report`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { DamageReport } from './damage-report.class'
import createModel from '../../models/damage-report.model'
import hooks from './damage-report.hooks'
import authAPI from '../../middleware/api-auth'
import * as helper from '../../helper/helper'
import configs from '../../configs'

let xlsx = require('json-as-xlsx')
import moment from 'moment'
import S3AWS from '../../library/s3AWS'

const s3 = new S3AWS()
const now = Math.round(+new Date() / 1000)

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'damage-report': DamageReport & ServiceAddons<any>;
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$populate', '$options'],
  }

  // Initialize our service with any options it requires
  app.use('/damage-report', new DamageReport(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('damage-report')

  /**
   * @api {post} /damage-report make new damage report
   * @apiName CreateDamageReport
   * @apiGroup DamageReport
   * @apiHeader {String} Authorization token of user
   *
   * @apiParam {String} trailerId id of trailer.
   * @apiParam {String} trailerNo trailer No.
   * @apiParam {String} companyId company Id.
   * @apiParam {String} companyNo company No.
   * @apiParam {String} description description for report.
   * @apiParam {String} reportId reportId for report.
   * @apiParam {Date} submittedDate submittedDate.
   * @apiParam {Array} images images.
   *
   * @apiSuccess {Object} data of damage report.
   */

  /**
   * @api {patch} /damage-report/{id} make new damage report
   * @apiName UpdateDamageReport
   * @apiGroup DamageReport
   * @apiHeader {String} Authorization token of user
   *
   * @apiParam {String} trailerId id of trailer.
   * @apiParam {String} trailerNo trailer No.
   * @apiParam {String} companyId company Id.
   * @apiParam {String} companyNo company No.
   * @apiParam {String} description description for report.
   * @apiParam {Date} submittedDate submittedDate.
   * @apiParam {Number} status draft: 0,new: 1,approved: 2,closed: 3,canceled: 4.
   * @apiParam {Array} images images.
   *
   * @apiSuccess {Object} data of damage report.
   */

  /**
   * @api {get} /damage-report/{id} get detail damage report
   * @apiName DetailDamageReport
   * @apiGroup DamageReport
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {Object} data of damage report.
   */


  /**
   * @api {get} /damage-report get list damage report
   * @apiName ListDamageReport
   * @apiGroup DamageReport
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {Object} data of damage report.
   */

  /**
   * @api {get} /damage-report-export-xls export trailer damage-report to exel file
   * @apiName Export damage-report to xlsx file
   * @apiGroup DamageReport
   *
   * @apiSuccess {Object} url of link download.
   */

  app.get('/damage-report-export-xls', authAPI, async (req: any, res: any) => {
    const query: any = {}
    if (req.user.company) query.companyId = req.user.company
    let reports = await service.Model.find(query).lean()
    let arrSheet: any[] = []
    let companyRequest = {
      url: `${app.get('CTRUrl')}/company`,
      method: 'GET',
      token: req.headers.authorization
    }
    let companies = await helper.request(companyRequest)
    await Promise.all(reports.map(async (item: any) => {
      let companyName = null
      companies.data.map((company: any) => {
        if (item.companyId && (item.companyId.toString() === company._id.toString())) companyName = company.name
      })
      let lastRepair: any = await app.service('repair-report').Model.findById(item.lastRepairReportId).exec()
      let status = helper.getKeyByValue(configs.statusDamageReport, item.status)
      let row = {
        damageReport: item.reportId,
        trailerNo: item.trailerNo,
        companyName: companyName,
        status: status,
        lastRepairReportId: lastRepair ? lastRepair.reportId : null
      }
      arrSheet.push(row)
    }))

    const data: any[] = [
      {
        sheet: 'Damage Report list',
        columns: [
          { label: 'damageReport', value: 'damageReport' },
          { label: 'trailerNo', value: 'trailerNo' },
          { label: 'companyName', value: 'companyName' },
          { label: 'status', value: 'status' },
          { label: 'lastRepairReportId', value: 'lastRepairReportId' }
        ],
        content: arrSheet
      }
    ]

    const settings: any = {
      writeOptions: {
        type: 'buffer',
        bookType: 'xlsx'
      }
    }
    const buffer = xlsx(data, settings)
    let url = await s3.upload(buffer, `${now}_damageReport${moment(new Date()).format('DD-MM-YYYY')}.xlsx`)
    res.json({ url })
  })

  service.hooks(hooks)
}
