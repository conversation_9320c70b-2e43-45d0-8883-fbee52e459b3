// Initializes the `trailer-monitor` service on path `/trailer-monitor`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerMonitor } from './trailer-monitor.class';
import hooks from './trailer-monitor.hooks';
import createModel from '../../models/trailer-monitor.model';
import authAPI from "../../middleware/api-auth";
import configs from "../../configs";
let xlsx = require("json-as-xlsx");
import * as helper from "../../helper/helper";
import moment from "moment";
import S3AWS from "../../library/s3AWS";
import { prefixLatLng } from '../../helper/helper'
const s3 = new S3AWS();
const now = Math.round(+new Date()/1000);
import Redis from '../../library/redis'
import * as Response from '../../helper/response'


// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-monitor': TrailerMonitor & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };


  // Initialize our service with any options it requires
  app.use('/trailer-monitor', new TrailerMonitor(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-monitor');

  /**
   * @api {get} /trailer-monitor-export-xls export trailer monitor to exel file
   * @apiName Export trailer monitor to xlsx file
   * @apiGroup TrailerMonitor
   *
   * @apiSuccess {Object} url of link download.
   */

  app.get('/trailer-monitor-export-xls',authAPI, async (req:any, res:any) => {
    try {
      const redis = new Redis()
      let trailerMonitor: any = await service._find({
        ...req,
        token: req.headers.authorization,
        query: { limit: 1000 }
      })

      console.log('arrSheet')
      let arrSheet: any[] = [];
      let url: any = await redis.client.get(`trailer-monitor-export-${req.user._id}`);
      // if (url) {
      //   res.json({ url })
      //   return
      // }

      let companyRequest = {
        url: `${app.get('CTRUrl')}/company`,
        method: 'GET',
        token: req.headers.authorization
      }
      let companies = await helper.request(companyRequest);
      const arrCompanies = companies.data;

      await Promise.all(trailerMonitor.data.map( async (item:any) => {
        let dataTrailer = item.trailerNumber;
        const cpn = arrCompanies.find((element: any) => element._id == dataTrailer.company )
        let row = {
          trailerNo: dataTrailer.trailerNumber,
          company: cpn.name,
          size: dataTrailer.trailerSize ? dataTrailer.trailerSize.name : null,
          status: dataTrailer.trailerStatus ? dataTrailer.trailerStatus.name : null,
          type: dataTrailer.trailerType ? dataTrailer.trailerType.name : null,
          indicator: dataTrailer.color,
          vehicleNo : dataTrailer.truckNumber ? dataTrailer.truckNumber.vehicleNo : null,
          location: item.locationName,
          remarks1: item.remarks1 ? item.remarks1 : null,
          remarks2: item.remarks2 ? item.remarks2 : null,
          referenceDateTime1: item.referenceDateTime1 ?  moment(item.referenceDateTime1).format("DD-MM-YYYY hh:mm") : null,
          referenceDateTime2: item.referenceDateTime2 ? moment(item.referenceDateTime2).format("DD-MM-YYYY hh:mm") : null,
          updatedAt: item.updatedAt ?  moment(item.updatedAt).format("DD-MM-YYYY hh:mm") : null,
        }
        arrSheet.push(row);
      }))
      const data: any[] = [
        {
          sheet: "TrailerMonitor list",
          columns: [
            { label: "Trailer No.", value: "trailerNo" },
            { label: "Company", value: "company" },
            { label: "Size", value: "size" },
            { label: "Status", value: "status" },
            { label: "Type", value: "type" },
            { label: "Indicator", value: "indicator" },
            { label: "Location", value: "location" },
            { label: "Vehicle No", value: "vehicleNo" },
            { label: "Remarks 1", value: "remarks1" },
            { label: "Remarks 2", value: "remarks2" },
            { label: "Ref Date 1", value: "referenceDateTime1"},
            { label: "Ref Date 2", value: "referenceDateTime2" },
            { label: "Timestamp", value: "updatedAt"}
          ],
          content: arrSheet,
        }
      ]

      const settings: any = {
        writeOptions: {
          type: "buffer",
          bookType: "xlsx",
        },
      }
      const buffer = xlsx(data, settings);
      url = await s3.upload(buffer, `${now}_trailerMonitor${moment(new Date()).format("DD-MM-YYYY")}.xlsx`);
      redis.client.set(`trailer-monitor-export-${req.user._id}`, url, {EX: 15 * 60})
      Response.success(res, {url}, 200)
    } catch (error: any) {
      Response.error(res, error, error.code)
    }
  });

  app.get('/trailers-overview', authAPI, async (req: any, res: any) => {
    try {
      let param = { token: req.headers.authorization, query: { limit: 1000 } }
      let result = await service._find(param)
      Response.success(res, result, 200)
    } catch (error: any) {
      Response.error(res, error, error.code)
    }
  })



  service.hooks(hooks);
}
