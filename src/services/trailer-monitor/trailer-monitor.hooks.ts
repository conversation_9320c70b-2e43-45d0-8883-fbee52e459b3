import { HooksObject } from '@feathersjs/feathers';
import { authorizeByRole } from '../../middleware/permission'

export default {
  before: {
    all: [
      async (context: any) => {
        try {
          await authorizeByRole(context)
          return context
        } catch (error) {
          throw error
        }
      }
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
