import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import * as helper from '../../helper/helper'
import { Params } from '@feathersjs/feathers'
import _ from 'lodash'
import {
  CTR_RESPONSE,
  prefixLatLng,
  prefixRoadNameOf,
  prefixZoneNameOf
} from '../../helper/helper'
import Redis from '../../library/redis'
import fs from 'fs/promises'
import path from 'path'
import { NotFound } from '@feathersjs/errors'

// Define the cache file path
const CACHE_FILE_PATH = path.join(
  __dirname,
  '../../../cache/trailer-monitor-all-response.json'
)

// Ensure cache directory exists
const ensureCacheDir = async () => {
  try {
    await fs.mkdir(path.dirname(CACHE_FILE_PATH), { recursive: true })
  } catch (err) {
    // Directory already exists, do nothing
  }
}

// Read cache file
const readCacheFile = async () => {
  try {
    const data = await fs.readFile(CACHE_FILE_PATH, 'utf8')
    return JSON.parse(data)
  } catch (err) {
    return null
  }
}

// Write cache file
const writeCacheFile = async (data: any) => {
  await ensureCacheDir()
  await fs.writeFile(CACHE_FILE_PATH, JSON.stringify(data))
}

export class TrailerMonitor extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: any

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async findTrailerMonitorInlist(param: any) {
    console.log('param.token', param.token)
    try {
      let userRequest = {
        method: 'GET',
        url: this.app.get('urlAuth'),
        token: param.token
      }
      let resultUser = await helper.request(userRequest)
      console.log(CTR_RESPONSE + 'resultUser: ', resultUser.data)
      let urlTrailerMonitor = `${this.app.get(
        'CTRUrl'
      )}/trailer-management?$sort[updatedAt]=-1&$limit=${
        param.query.limit || 10
      }&$skip=${param.query.skip || 0}`
      // delete param.query.limit
      // delete param.query.skip
      delete param.query.$sort
      if (param.query.trailerNumber) {
        urlTrailerMonitor += `&trailerNo=${param.query.trailerNumber}`
        delete param.query.trailerNumber
      }
      if (resultUser.data?.user?.company)
        urlTrailerMonitor += `&company=${resultUser.data.user.company}`
      for (const key in param.query) {
        urlTrailerMonitor += `&${key}=${param.query[`${key}`]}`
      }

      let arrBlackList: any = await this.Model.find({
        createdBy: resultUser.data.user._id
      }).lean()
      let blackIds = _.map(arrBlackList, 'trailerMonitorId')
      for (const id of blackIds) {
        urlTrailerMonitor += `&_id[$nin][]=${id}`
      }

      if (param.query.ids) {
        for (const id of param.query.ids) {
          urlTrailerMonitor += `&_id[$in][]=${id}`
        }
      }

      let data = {
        method: 'GET',
        url: urlTrailerMonitor,
        token: param.token
      }
      let trailerMonitor = await helper.request(data)
      let result: any = {
        total: trailerMonitor.data.total,
        list: trailerMonitor.data.data,
        user: resultUser.data.user
      }
      return result
    } catch (e: any) {
      if (e.response?.data) throw e.response.data
      throw e
    }
  }

  async listFilterByBlackList(arrList: any[], user: any) {
    let arrBlackList: any = await this.Model.find({
      createdBy: user._id
    }).lean()
    let arrResult = arrList
    if (arrBlackList.length > 0) {
      let arrTrailerMonitorIds: any[] = []
      arrBlackList.map((blackList: any) => {
        arrTrailerMonitorIds.push(blackList.trailerMonitorId.toString())
      })
      arrResult = []
      arrList.map((item: any) => {
        if (!arrTrailerMonitorIds.includes(item._id)) arrResult.push(item)
      })
    }
    return arrResult
  }

  async find(params: any) {
    return this._find(params)
  }

  async _find(param: any) {
    const redis = new Redis()

    // Get result from file cache instead of Redis
    if (param.query.limit == 1000 && !param.query.isCache) {
      const resultFromFile = await readCacheFile()
      if (resultFromFile) return resultFromFile
    }

    const locationIds = []
    const vehicleIds = []
    // Read from file cache instead of Redis
    const resultFromFile = await readCacheFile()
    const { data = [] } = resultFromFile || {}

    const MIN_LENGTH_SEARCH = 2
    const locationName =
      param.query.locationName &&
      param.query.locationName?.length > MIN_LENGTH_SEARCH
        ? param.query.locationName
        : null
    if (locationName) {
      const locationNameFilter = (a: any) =>
        a.locationName.toUpperCase().includes(locationName.toUpperCase())

      const locationNameFiltered = data.filter(locationNameFilter)
      locationIds.push(...locationNameFiltered.map((a: any) => a._id))
    }
    const vehicleNo = param.query.vehicleNo
    if (vehicleNo) {
      const vehicleNoFilter = (a: any) =>
        a.trailerNumber.truckNumber?.vehicleNo
          ?.toUpperCase()
          .includes(vehicleNo.toUpperCase())

      const vehicleNoFiltered = _.filter(data, vehicleNoFilter)
      vehicleIds.push(..._.map(vehicleNoFiltered, '_id'))
    }

    let ids
    if (vehicleNo) ids = vehicleIds
    if (locationName) ids = locationIds
    if (locationName && vehicleNo) ids = _.intersection(vehicleIds, locationIds)
    if (vehicleNo || locationName) {
      // @ts-ignore
      if (!ids.length)
        return {
          total: 0,
          limit: param.query.limit || 10,
          skip: param.query.skip || 0,
          data: []
        }
      param.query.ids = ids
    }

    let listTrailerAndUser = await this.findTrailerMonitorInlist(param)

    const zoneService = this.app.service('zone-management')
    await zoneService.getArrCompanies(param.token)
    const companiesInRedis = await redis.client.get('companies')
    let companies = companiesInRedis ? JSON.parse(companiesInRedis) : null

    const monitorHistoryService = this.app.service('trailer-monitor-history')
    const arrList = listTrailerAndUser.list

    const returnArray = []
    const batchSize = 20
    for (let i = 0; i < arrList.length; i += batchSize) {
      const batch = arrList.slice(i, i + batchSize)
      const smallerArr = await Promise.all(
        batch.map(async (item: any) => {
          let trailer: any
          try {
            trailer = JSON.parse(JSON.stringify(item))
            const [
              location,
              countHistory,
              cpn,
              lastEditor,
              lastLocation,
              locationRecordedAt
            ] = await Promise.all([
              // get location
              await (async () => {
                const zoneKey = `${prefixZoneNameOf}${trailer.trailerNumber.trailerNumber}`
                const locationKey = `${prefixLatLng}${trailer.trailerNumber.trailerNumber}`
                try {
                  let zoneNameOf: string | null
                  if (param.user?.company) {
                    zoneNameOf = await redis.client.get(
                      `${zoneKey}-${param.user.company}`
                    )
                  } else {
                    zoneNameOf = await redis.client.get(`${zoneKey}`)
                  }
                  if (zoneNameOf) return zoneNameOf

                  const roadNameOf = await redis.client.get(
                    `${prefixRoadNameOf}${trailer.trailerNumber.trailerNumber}`
                  )
                  if (roadNameOf) return roadNameOf

                  const locationRedis = await redis.client.get(`${locationKey}`)
                  if (!locationRedis)
                    throw new NotFound('Location not in redis')
                  const coordinates = JSON.parse(
                    JSON.parse(locationRedis).location
                  ).coordinates
                  trailer.coordinates = coordinates
                  const [lng, lat] = coordinates
                  //Get zone name
                  let result = await zoneService.findPointInArrCompanies(
                    Number(lat),
                    Number(lng),
                    trailer.trailerNumber.company,
                    !Boolean(param.user?.company)
                  )
                  if (Object.keys(result).length && result.zoneName) {
                    if (param.user?.company) {
                      redis.client.set(
                        `${zoneKey}-${param.user.company}`,
                        'ZONE_' + result.zoneName,
                        { EX: 60 * 10 }
                      )
                    } else {
                      redis.client.set(
                        `${zoneKey}`,
                        'ZONE_' + result.zoneName,
                        { EX: 60 * 10 }
                      )
                    }
                    return 'ZONE_' + result.zoneName
                  }
                  //Road Name

                  let roadNameRequest = {
                    method: 'POST',
                    url: `${this.app.get('roadUrl')}/road-map/name`,
                    token: param.token,
                    headers: {
                      'request-from': '_find trailer-monitor'
                    },
                    body: {
                      latitude: `${lat}`,
                      longitude: `${lng}`
                    }
                  }
                  let resultRoadName = await helper.request(roadNameRequest)
                  const roadName = resultRoadName.data?.name
                    ? 'ROAD_' + resultRoadName.data.name
                    : 'ROAD_NOT_FOUND'
                  redis.client.set(
                    `${prefixRoadNameOf}${trailer.trailerNumber.trailerNumber}`,
                    roadName,
                    { EX: 60 * 10 }
                  )
                  return roadName
                } catch (err: any) {
                  if (err.message?.includes('Location not in redis'))
                    return 'No Location Provided'
                  // console.error('NO_LOCATION error: ', err.message)
                  return 'NO_LOCATION'
                }
              })(),
              // count history
              await monitorHistoryService.Model.count({
                trailerId: trailer?.trailerNumber?._id
              }),
              companies.data.find((cpn: any) => {
                if (cpn._id == trailer.trailerNumber.company) return cpn
              }),
              (async () => {
                const lastUpdateHistory =
                  await monitorHistoryService.Model.find({
                    trailerId: trailer?.trailerNumber?._id
                  })
                    .sort({ createdAt: -1 })
                    .lean()
                if (lastUpdateHistory.length && lastUpdateHistory[0].createdBy)
                  return lastUpdateHistory[0].createdBy
                return trailer.trailerNumber?.createdBy
              })(),
              (async () => {
                const lastLocation = await redis.client.get(
                  `${prefixLatLng}${trailer.trailerNumber.trailerNumber}`
                )
                return lastLocation ? JSON.parse(lastLocation).location : null
              })(),
              (async () => {
                const lastLocation = await redis.client.get(
                  `${prefixLatLng}${trailer.trailerNumber.trailerNumber}`
                )
                return lastLocation ? JSON.parse(lastLocation).recordedAt : null
              })()
            ])

            trailer.locationName = location
            trailer.countHistory = countHistory
            trailer.company = cpn.name
            trailer.lastLocation = lastLocation
            trailer.updatedBy = lastEditor
            trailer.locationRecordedAt = locationRecordedAt
            trailer.etsRecordTime = new Date()

            return trailer
          } catch (err: any) {
            console.log('err', err)
            return trailer
          }
        })
      )
      returnArray.push(...smallerArr)
    }

    const response = {
      total: listTrailerAndUser.total,
      limit: param.query.limit || 10,
      skip: param.query.skip || 0,
      data: returnArray
    }

    // Save to file instead of Redis
    if (param.query.isCache) {
      try {
        await writeCacheFile(response)
        this.app.trailerMonitorAll = response
        // @ts-ignore
        this.app.set('trailerMonitorAll', response)
        console.log('saved trailerMonitorAll to file')
      } catch (err) {
        console.error('Failed to save trailer monitor cache file:', err)
      }
    }

    return response
  }

  async create(data: any, param: Params) {
    try {
      //remove from black list
      return await this.Model.remove({ trailerMonitorId: data.id })
    } catch (e: any) {
      throw e.response.data
    }
  }

  async remove(_trailerNumber: string, param: Params) {
    //ad trailer to the list don't want to see
    try {
      let userRequest = {
        method: 'GET',
        url: this.app.get('urlAuth'),
        token: param.token
      }
      let resultUser = await helper.request(userRequest)
      let createdBy = resultUser.data.user._id
      // let requestDetailTrailer = {
      //   method: 'GET',
      //   url: `${this.app.get('CTRUrl')}/trailer-master?trailerNumber=${_trailerNumber}&deleted=false`,
      //   token: param.token
      // }
      // let trailerDetail: any = await helper.request(requestDetailTrailer)
      // let trailerId = trailerDetail.data.data[0]._id
      // let requestDetailMonitor = {
      //   method: 'GET',
      //   url: `${this.app.get('CTRUrl')}/trailer-management?trailer=${trailerId}`,
      //   token: param.token
      // }
      // let [err, trailerRelation] = await wait(helper.request(requestDetailMonitor))
      // if (err) throw err
      let dataInsert: any = {
        trailerMonitorId: _trailerNumber,
        // trailerId: trailerId,
        createdBy
      }
      if (resultUser.data.user.company)
        dataInsert.companyId = resultUser.data.user.company
      return await super.create(dataInsert)
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async get(id: string, param: Params) {
    try {
      let requestDetail = {
        method: 'GET',
        url: `${this.app.get('CTRUrl')}/trailer-management/${id}`,
        token: param.token
      }
      let detailTrailer = await helper.request(requestDetail)
      return detailTrailer.data
    } catch (e: any) {
      throw e.response.data
    }
  }

  async update(trailerId: string, newData: any, param: Params) {
    try {
      // add previous to history
      let requestDetail = {
        method: 'GET',
        url: `${this.app.get(
          'CTRUrl'
        )}/trailer-management?trailer=${trailerId}`,
        token: param.token
      }
      let resultDetail = await helper.request(requestDetail)
      let monitorId = resultDetail.data.data[0]._id
      let detail = resultDetail.data.data[0]

      let userRequest = {
        method: 'GET',
        url: this.app.get('urlAuth'),
        token: param.token
      }
      let resultUser = await helper.request(userRequest)
      let createdBy = resultUser.data.user._id

      detail = {
        ...detail,
        trailerId: trailerId,
        createdBy
      }
      delete detail.createdAt
      delete detail.updatedAt
      delete detail._id

      // if (newData.indicator) {
      //   let updateColorRequest = {
      //     method: 'PATCH',
      //     url: `${this.app.get('CTRUrl')}/trailer-master/${trailerId}`,
      //     token: param.token,
      //     body: { color: newData.indicator }
      //   }
      //   await helper.wait(helper.request(updateColorRequest))
      // }

      // newData.trailer = newData._id;
      delete newData._id

      let requestUpdate = {
        method: 'PATCH',
        url: `${this.app.get('CTRUrl')}/trailer-management/${monitorId}`,
        token: param.token,
        body: newData
      }
      let [err, updateTrailer] = await helper.wait(
        helper.request(requestUpdate)
      )
      if (err) {
        console.info('Error payload requestUpdate ', requestUpdate)
        console.info('Body ', newData)
        console.error('Error ', err.response.data)
        throw err
      }
      await this.app.service('trailer-monitor-history')._create(detail)
      return updateTrailer.data
    } catch (e: any) {
      //console.log(e);
      throw e
    }
  }
}
