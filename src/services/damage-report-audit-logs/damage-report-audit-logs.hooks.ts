import {HookContext, HooksObject} from '@feathersjs/feathers';
// import auth from "../../middleware/auth";

export default {
  before: {
    all: [
      // async (context: HookContext) => {
      //   return await auth(context);
      // }
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [
      async (context: HookContext) => {
        context.result.arrImages = await context.app.service('images').Model.find({
          _id: {$in: context.result.imageArrId}
        }).lean();
      }
    ],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
