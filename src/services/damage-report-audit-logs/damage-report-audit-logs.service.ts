// Initializes the `damage-report-audit-logs` service on path `/damage-report-audit-logs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { DamageReportAuditLogs } from './damage-report-audit-logs.class';
import createModel from '../../models/damage-report-audit-logs.model';
import hooks from './damage-report-audit-logs.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'damage-report-audit-logs': DamageReportAuditLogs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$populate', '$options'],
  };

  // Initialize our service with any options it requires
  app.use('/damage-report-audit-logs', new DamageReportAuditLogs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('damage-report-audit-logs');

  service.hooks(hooks);
}
