import { Service, MongooseServiceOptions } from 'feathers-mongoose';
import { Application } from '../../declarations';
import {Params} from "@feathersjs/feathers";

export class TrailerMonitorConfig extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: any;
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options);
    this.app = app;
  }

  // async create(data:any,param:Params) {
  //   try {
  //     let user = await this.app.service('system-ctr').find({query:{jwt:param.token}});
  //     let userId = user.data[0].userId;
  //     return await super.create({
  //       userId: userId,
  //       config: data.config
  //     });
  //   }catch (e:any) {
  //     throw e;
  //   }
  // }

  async update(id: string,data:any,param:Params) {
    try {
      let user = await this.app.service('system-ctr')._find({query:{jwt:param.token}});
      let userId = user.data[0].userId;
      data = {... data,
        userId : userId
      };
      let config:any = await super._find({
        query: {
          $limit: 1,
          userId: userId
        }
      });
      return await super._update(config.data[0]._id,data);
    }catch (e:any) {
      throw e;
    }
  }

  async get(id:any,param:Params) {
    try {
      let user = await this.app.service('system-ctr')._find({query:{jwt:param.token}});
      let userId = user.data[0].userId;
      let config:any = await super._find({
        query: {
          $limit: 1,
          userId: userId
        }
      });
      if (config.data.length > 0) return config.data[0];
      return {};
    }catch (e:any) {
      throw e;
    }
  }
}
