// Initializes the `trailer-monitor-config` service on path `/trailer-monitor-config`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerMonitorConfig } from './trailer-monitor-config.class';
import createModel from '../../models/trailer-monitor-config.model';
import hooks from './trailer-monitor-config.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-monitor-config': TrailerMonitorConfig & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/trailer-monitor-config', new TrailerMonitorConfig(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-monitor-config');

  service.hooks(hooks);
}
