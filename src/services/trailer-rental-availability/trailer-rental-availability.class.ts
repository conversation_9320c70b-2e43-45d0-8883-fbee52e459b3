import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { sendErrorToWebhook } from '../../helper/helper'
import createApplication, { Id } from '@feathersjs/feathers'
import _ from 'lodash'
import Redis from '../../library/redis'
import {
  IPatchTrailerRentalAvailability,
  ITrailerRentalAvailability
} from '../../interface/trailer-rental-availability'


export class TrailerRentalAvailability extends Service<ITrailerRentalAvailability> {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async find(params: any) {
    const redis = new Redis()
    const resultInRedis =
      (await redis.client.get('trailer-monitor-all-response')) || null
    const arrLocationName = resultInRedis
      ? _.map(JSON.parse(resultInRedis.toString())?.data, (tr) => {
          return {
            trailerNumber: tr.trailerNumber.trailerNumber,
            lastLocation: tr.lastLocation,
            locationName: tr.locationName
          }
        })
      : []
    params.query = {
      ...params.query,
      $populate: [{
        path: 'rentalHistory',
        select: ['start', 'end', 'rentee', 'owner']
      }]
    }

    const qrLocationName = params.query.locationName
    if (qrLocationName) {
      const MIN_LENGTH_SEARCH = 2
      const locationName = (qrLocationName.length > MIN_LENGTH_SEARCH) ? qrLocationName : null
      if (locationName) {
        const newArrTlNos = _.map(_.filter(arrLocationName, (tr) => tr.locationName.toUpperCase().includes(locationName.toUpperCase())), 'trailerNumber')
        if (params.query['trailer.trailerNumber']) {
          const crArrTlNos = params.query['trailer.trailerNumber'].$in
          params.query['trailer.trailerNumber'].$in = _.intersection(crArrTlNos, newArrTlNos)
        } else
        params.query['trailer.trailerNumber'] = { $in:  newArrTlNos}
      }
    }

    const tra = await super.find(params)
    try {
      // @ts-ignore
      tra.data = await Promise.all(_.map(tra.data, async (trailer1) => {
        const obj2 = _.find(arrLocationName, { trailerNumber: trailer1.trailerNumber })
        return _.merge(trailer1, {
          activeRentals: await this.app.service('rental').getActiveRental(trailer1._id),
          locationName: obj2?.locationName || 'NO_LOCATION',
          lastLocation: obj2?.lastLocation || null
        })
      }))
    } catch (e: any) {
      await sendErrorToWebhook('Error get location in trailer-availability', e.message)
    }

    return tra
  }

  // async create(data: any, params: Params) {
  //   data.trailerNumber = data?.trailer?.trailerNumber?.company
  //   if (params.user && params.user.company) {
  //     data.ownerId = params.user.company
  //     if (data.trailer.company._id !== params.user.company) {
  //       throw new Error('This trailer is not belong to your company')
  //     }
  //   }
  //   return super._create(data, params)
  // }

  async create(data: ITrailerRentalAvailability | ITrailerRentalAvailability[], params: any) {
    return this._create(data, params)
  }

  async _create(data: ITrailerRentalAvailability | ITrailerRentalAvailability[], params: any) {
    data = Array.isArray(data) ? data : [data]
    const resultPatch: any[] = []
    const listTra = await this.Model.find({
      'trailer._id': { $in: _.map(data, 'trailer._id') }
    }).lean()
    const aTra = (await Promise.all(await data.map(async (tr) => {
      tr.trailerNumber = tr.trailer.trailerNumber
      tr.updatedBy = params?.user?._id
      const tra = await this.Model.findOne({
        'trailer._id': tr.trailer._id
      }).lean()
      if (tra) {
        tr.deletedAt = null
        tr.deletedBy = null
        resultPatch.push(await this._patch(tra._id, tr))
      } else {
        tr.createdBy = params?.user?._id
        return tr
      }
    }))).filter(a => a != undefined) as ITrailerRentalAvailability[]
    const resultCreate = await super._create(aTra, params)
    return [...[resultCreate].flat(), ...resultPatch]
  }

  async _patch(id: Id, data: IPatchTrailerRentalAvailability, params?: any) {
    const oldTRA = await this._get(id)
    data.updatedBy = params?.user?._id //user undefined once cronjob update
    // await this.saveHistory(id)
    const result  = await super._patch(id, data, params)
    if (!Array.isArray(result)) result.origin = oldTRA
    return result
  }

  async patch(id: Id, data: IPatchTrailerRentalAvailability, params?: any) {
    return this._patch(id, data, params)
  }


  // async _remove(id: string, params?: any): Promise<any[] | any> {
  //
  //   const data = {
  //     deletedBy: params?.user?._id,
  //     deletedAt: new Date(),
  //     availableForRentTo: [],
  //   }
  //
  //   return super._patch(id, data, params)
  // }

  async removeMultiple(ids: string[], params?: any){

    const removedData = {
      deletedBy: params?.user?._id,
      deletedAt: new Date(),
      availableForRentTo: []
    }

    const tras = await this.Model.find({_id: {$in: ids}}).lean()
    const trIds = _.map(tras, 'trailer._id')
    const trNos = _.map(tras, 'trailer.trailerNumber')
    const resultRemove = await this.Model.updateMany({_id: {$in: ids}}, removedData)
    const updateTrailerSharing = await this.app.service('trailer-sharing').Model.updateMany({
      trailers: {
        $elemMatch: {
          _id: {$in: trIds}
        }
      }
    }, {
      $pull: {
        trailers: {
          trailerNo: {$in: trNos}
        },
        trailerNos: {$in: trNos},
        trailerIds: {$in: trIds}
      }
    })

    return
  }

  async _find(params?: createApplication.Params): Promise<createApplication.Paginated<ITrailerRentalAvailability>> {
    // @ts-ignore
    return super._find(params)
  }

}
