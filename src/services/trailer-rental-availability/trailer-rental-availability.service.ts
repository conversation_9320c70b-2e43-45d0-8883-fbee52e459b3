// Initializes the `trailers` service on path `/trailer-rental-availability`
import { Paginated, ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { TrailerRentalAvailability } from './trailer-rental-availability.class'
import createModel from '../../models/trailer-rental-availability.model'
import hooks from './trailer-rental-availability.hooks'
import authAPI from '../../middleware/api-auth'
import { checkAccessibleDynamicSharing, isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'
import { sendErrorToWebhook } from '../../helper/helper'
import { CTVRental } from '../../models/rentals.model'
import _ from 'lodash'
import { NotFound } from '@feathersjs/errors'
import { ITrailerRentalAvailability } from '../../interface/trailer-rental-availability'
import { IRental } from '../../interface/rental'
import { ITrailerSharing } from '../../interface/trailer-sharing'
import logger from '../../logger'
import * as Response from '../../helper/response'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-rental-availability': TrailerRentalAvailability & ServiceAddons<ITrailerRentalAvailability>;
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
    multi: ['create', 'remove'],
    lean: true
  }


  app.patch('/trailer-rental-availability/multiple', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    try {
      const newUpdate = req.body
      const ids = req.query.ids
      const listOfTRA = await app
        .service('trailer-rental-availability')
        .Model.find({ _id: { $in: ids } })
        .lean()
      const updatedRecords: any = []

      await Promise.all(
        listOfTRA.map(async (tra: any) => {
          const updateRentingDetails = {
            ...tra.rentingDetails,
            ...newUpdate.rentingDetails
          }
          const record = await app
            .service('trailer-rental-availability')
            ._patch(
              tra._id,
              {
                ...newUpdate,
                rentingDetails: updateRentingDetails
              },
              req
            )
          updatedRecords.push(record)
        })
      )
      res.json({ updatedRecords })
    } catch (error: any) {
      Response.error(res, error, error.code)
    }
  })

  app.get('/trailer-rental-availability/management', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    try {
      const params = {
        headers: req.headers,
        user: req.user,
        query: {
          ...req.query
        }
      }
      if (req.user.company) {
        params.query.ownerId = req.user.company
      }
      const isLeasing = req.query.isLeasing === 'true'
      if (isLeasing) {
        params.query.isLeasing = req.query.isLeasing === 'true'
      }

      return res.json(await service.find(params))
    } catch (error: any) {
      await sendErrorToWebhook('Error availability/management', { error: error.message })
      Response.error(res, error, error.code)
    }
  })
  // API not in use from FE
  app.get('/trailer-rental-availability/view', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    const params = {
      headers: req.headers,
      user: req.user,
      query: {
        ...req.query,
        isAvailable: true
      }
    }
    const company = req.user.company

    if (company) {
      params.query.ownerId = { $ne: company }

      const sharing = await app.service('trailer-sharing')._find({
        query: {
          sharedCompany: { '$in': [company] }
        }
      }) as Paginated<ITrailerSharing>

      const trailerNos: any = []
      const today = new Date()
      // sharing.data.map((s: any) => {
      //   const listTrA = s.trailers.filter((a: any) => a.startDate.getTime() >= today.getTime() && a.endDate.getTime() <= today.getTime())
      // })
      trailerNos.push(..._.map(sharing.data, 'trailerNo'))

      params.query['trailer.trailerNumber'] = { $in: trailerNos }

    }
    const result = await service.find(params)
    Response.success(res, result, 200)
  })


  app.get('/trailer-rental-availability/view/available', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    const params = {
      headers: req.headers,
      user: req.user,
      query: {
        ...req.query,
        isAvailable: true
      }
    }
    try {
      const company = req.user.company

      if (company) {
        if (!params.query.ownerId) params.query.ownerId = { $ne: company }
        // Check is shared
        const trailerNos: string[] = await app.service('trailer-sharing').getTrailerNosIsShared(company)
        let trReNos: string[] = []
        params.query['trailer.trailerNumber'] = { $in: _.difference(trailerNos, trReNos) }
      } else params.query.isLeasing = false

      const result = await service.find(params)
      Response.success(res, result, 200)
    } catch (error: any) {
      await sendErrorToWebhook('Error view/available', { error: error.message })
      // return res.status(error.code).json({ message: error.message })
      Response.error(res, error, error.code)
    }

  })

  // app.get('/trailer-rental-availability/view/mine-rent', authAPI, isAccessibleDMS, async (req: any, res: any) => {
  //   try {
  //     const company = req.user.company ? req.user.company : req.query['rental.rentee.company']
  //     const rentalStatus = req.query['rental.status']
  //     const rentalStart = req.query['rental.start']
  //     const rentalEnd = req.query['rental.end']
  //
  //     delete req.query['rental.rentee.company']
  //     delete req.query['rental.start']
  //     delete req.query['rental.end']
  //     delete req.query['rental.status']
  //
  //     const params = {
  //       user: req.user,
  //       query: {
  //         ...req.query,
  //         isLeasing: true,
  //         isAvailable: true
  //       }
  //     }
  //     const rentalService = app.service('rental')
  //     const rentalParams: any = {
  //       query: {
  //         $select: ['trailerRentalId'],
  //         $limit: 1000
  //       }
  //     }
  //
  //     if (company) rentalParams.query['rentee.company'] = company
  //     if (rentalStatus) rentalParams.query.status = rentalStatus
  //     if (rentalStart) rentalParams.query.start = rentalStart
  //     if (rentalEnd) rentalParams.query.end = rentalEnd
  //     if (rentalStatus || company || rentalStart || rentalEnd) {
  //       const rentals = await rentalService._find(rentalParams)
  //       let trvIds = rentals.data.map((rental: any) => rental.trailerRentalId.toString())
  //       params.query._id = { $in: trvIds }
  //     }
  //
  //     const result = await service.find(params)
  //     Response.success(res, result, 200)
  //   } catch (error: any) {
  //     await sendErrorToWebhook('Error view/mine-rent', { error: error.message })
  //     Response.error(res, error, error.code)
  //   }
  // })

  app.delete('/trailer-rental-availability/', authAPI, isAccessibleDMS, async (req: any, res: any) => {
    try {
      const ids = req.query.ids
      const result = await service.removeMultiple(ids, req)
      Response.success(res, result, 200)
    } catch (error: any) {
      await sendErrorToWebhook('Error delete trailer-rental-availability', { error: error.message })
      Response.error(res, error, error.code)
    }
  })
// Initialize our service with any options it requires
  app.use('/trailer-rental-availability', new TrailerRentalAvailability(options, app))
// Get our initialized service so that we can register hooks
  const service = app.service('trailer-rental-availability')
  service.hooks(hooks)
}
