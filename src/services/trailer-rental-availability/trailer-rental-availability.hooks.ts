import { HooksObject, HookContext } from '@feathersjs/feathers'
import { Conflict } from '@feathersjs/errors'
import _ from 'lodash'
import { softDelete } from 'feathers-hooks-common'
import { appendFilterAuditBy } from '../../middleware/hook-filter'
import { ICreateTrailerRentalAvailability, ITrailerRentalAvailability } from '../../interface/trailer-rental-availability'

async function updateTrS(context: HookContext) {
  try {
    const { result } = context
    const { deletedAt, trailer, origin, rentingDetails, trailerNumber: trailerNo } = result
    if (deletedAt) return context

    if (!_.isEqual(rentingDetails, origin.rentingDetails)) {
      const resultUpdate = await context.app.service('trailer-sharing').Model.updateMany({
        trailers: {
          $elemMatch: { trailerNo }
        }
      }, {
        $set: { 'trailers.$.rentingDetails': rentingDetails }
      })
    }

    const afrtNew = _.map(result.availableForRentTo, (a) => {
      return a.companyId.toString()
    }) || null
    const afrtOrigin = _.map(origin?.availableForRentTo, (a) => {
      return a.companyId.toString()
    }) || []

    const removedCpn = _.differenceBy(afrtOrigin, afrtNew)
    const addedCpn = _.differenceBy(afrtNew, afrtOrigin)

    const tsRemoveIds = _.map(await context.app.service('trailer-sharing').Model.find({
      trailers: {
        $elemMatch: {
          trailerNo: trailer.trailerNumber
        }
      },
      sharedCompany: {
        $in: removedCpn
      }
    }).lean(), '_id')

    const resultRemove = await Promise.all(tsRemoveIds.map((rmId) => {
      return context.app.service('trailer-sharing').Model.findByIdAndUpdate(rmId, {
        $pull: {
          trailers: {
            trailerNo: trailer.trailerNumber
          },
          trailerNos: trailer.trailerNumber,
          trailerIds: trailer._id
        }
      }).lean()
    }))

    const resultAdded = await Promise.all(addedCpn.map(async (addedCpnElement) => {
      return await context.app.service('trailer-sharing').Model.updateOne({
        sharedCompany: addedCpnElement,
        companyId: result.ownerId
      }, {
        $push: {
          trailers: {
            trailerNo: trailer.trailerNumber,
            rentingDetails: result.rentingDetails,
            _id: trailer._id
          },
          trailerNos: trailer.trailerNumber,
          trailerIds: trailer._id
        }
      }, { upsert: true })
    }))

    console.log('INPUT : ', JSON.stringify({ removedCpn, addedCpn }, null, 2))
    console.log('RESULT : ', JSON.stringify({ resultRemove, resultAdded }, null, 2))

  } catch
    (e: any) {
    console.log('updateTrS: ', e)
  }
}

async function removeInTrailerSharing(context: HookContext) {
  try {
    const { trailer, deletedAt } = context.result
    if (!deletedAt) return context
    const tsIds = _.map(await context.app.service('trailer-sharing').Model.find({
      trailers: {
        $elemMatch: {
          trailerNo: trailer.trailerNumber
        }
      }
    }).lean(), '_id')
    for (const tsId of tsIds) {
      const result = await context.app.service('trailer-sharing').patch(tsId, {
        $pull: {
          trailers: {
            trailerNo: trailer.trailerNumber
          },
          trailerNos: trailer.trailerNumber,
          trailerIds: trailer._id
        }
      }, context.params)
      // if (!result.trailers.length) await context.app.service('trailer-sharing')._remove(tsId, context.params)
    }
  } catch (e: any) {
    console.log('removeInTrailerSharing: ', e)
  }
  return context
}

async function createTrailerSharing(context: HookContext) {
  const { result } = context

  const tra = Array.isArray(result) ? result : [result]

  const createTsPl: any = {
    sharedCompanies: [],
    companyId: '',
    trailers: []
  }

  createTsPl.sharedCompanies = _.sample(tra)?.availableForRentTo?.map((a: any) => a.companyId)
  createTsPl.companyId = _.sample(tra)?.ownerId
  for (const dsTrailer of tra) {
    createTsPl.trailers.push({
      _id: dsTrailer.trailer._id,
      trailerNo: dsTrailer.trailer.trailerNumber,
      rentingDetails: dsTrailer.rentingDetails
    })
  }

  context.app.service('trailer-sharing').create(createTsPl, context.params)
}

export default {
  before: {
    all: [
      softDelete({
        // context is the normal hook context
        deletedQuery: async context => {
          return { deletedAt: null }
        },
        removeData: async (context: HookContext) => {
          return { deletedAt: new Date(), deletedBy: context.params?.user?._id, availableForRentTo: [] }
        }
      })

    ],
    find: [appendFilterAuditBy,
      async (context: any) => {
        const query = context.params.query
        if (query.trailerNumber) {
          query.trailerNumber = { $regex: query.trailerNumber, $options: 'i' }
        }
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [async function findTrailerRentals(context: HookContext): Promise<HookContext> {
      const trailerRentalAvailabilities = context.result.data || []

      try {
        const trailerRentalAvailabilityIds = trailerRentalAvailabilities.map((t: any) => t._id.toString())

        const rentalRequests = await context.app.service('rental-request').Model.find({
          trailerRentalId: { $in: trailerRentalAvailabilityIds },
          status: 'Pending'
        }).lean()

        const rentalRequestCounts = rentalRequests.reduce((counts: any, rentalRequest: any) => {
          const trailerRentalId = rentalRequest.trailerRentalId.toString()
          counts[trailerRentalId] = (counts[trailerRentalId] || 0) + 1
          return counts
        }, {})

        trailerRentalAvailabilities.forEach((trailerRentalAvailability: any) => {
          trailerRentalAvailability.rentalRequestPending = rentalRequestCounts[trailerRentalAvailability._id] || 0
        })

        context.result.data = trailerRentalAvailabilities
      } catch (error: any) {
        console.error(`An error occurred while processing the after find hook: ${error.message}`)
        console.error(error.stack)
        throw error
      }

      return context
    }
    ],
    get: [],
    create: [createTrailerSharing],
    update: [],
    patch: [removeInTrailerSharing, updateTrS],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
