import { HookContext, HooksObject } from '@feathersjs/feathers'
import auth from '../../middleware/auth'
import moment from 'moment'
import * as errors from '@feathersjs/errors'
import { CTVInspection } from '../../models/inspections.model'
import { CTVRental } from '../../models/rentals.model'

export default {
  before: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async (context: HookContext) => {
      try {
        const inspection = context.result
        const rentalService = context.app.service('rental')

        const status = inspection.type == CTVInspection.type.TakeOver ? CTVRental.status.InProgress : CTVRental.status.Returned
        await rentalService.internalPatch(inspection.rentalId, {
          assignee: inspection.inspector,
          status,
        }, context.params)
      } catch (error: any) {
        console.error(`An error occurred while processing the after create hook inspection: ${error.message}`)
        console.error(error.stack)
        throw error
      }

      return context
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
