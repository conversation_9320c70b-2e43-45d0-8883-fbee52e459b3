import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { Conflict, NotFound } from '@feathersjs/errors'
import { CTVRental } from '../../models/rentals.model'
import { ICreateInspection, IInspection } from '../../interface/inspection'
import S3AWS from '../../library/s3AWS'

// @ts-nocheck
export class Inspection extends Service<IInspection> {
  private app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async create(data: ICreateInspection, params?: any) {
    try {
      const rental = await this.app.service('rental')._get(data.rentalId)
      if (!CTVRental.isStatusAllowInspection(rental.status, data.type)) {
        throw new Conflict(
          `The rental cannot be inspected because its current status is "${rental.status}".`
        )
      }
      // validate images is not empty array
      if (!data.images || data.images.length === 0) {
        throw new Conflict('Images is required')
      }

      // find the images to make sure they not exist in other inspection
      const inspections = await this.Model.find({
        rental: data.rentalId
      }).lean()
      const images = inspections
        .map((inspection: any) => inspection.images)
        .flat()
      const duplicateImages = data.images.filter((image) =>
        images.includes(image)
      )
      if (duplicateImages.length > 0) {
        throw new Conflict(
          `The images ${duplicateImages.join(
            ', '
          )} already exist in other inspection`
        )
      }

      data.rentee = rental.rentee
      data.trailerNumber = rental.trailerNumber
      data.owner = rental.owner
    } catch (error: any) {
      if (error.code === 404) throw new NotFound('The rental ID is invalid')
      throw error
    }

    return super.create(data, params)
  }

  async findInspectionForRental(
    rentalId: string,
    useS3Link: boolean
  ): Promise<IInspection[]> {
    const s3 = new S3AWS()
    const inspections = await this.Model.find({ rentalId }).lean()
    // @ts-ignore
    if (!useS3Link) return inspections
    const inspectionsWithUrls = await Promise.all(
      // @ts-ignore
      inspections.map(async (inspection: IInspection) => {
        inspection.images = await Promise.all(
          inspection.images.map(
            (image) => s3.getUrl(image, 5 * 60) as Promise<string>
          )
        )
        return inspection
      })
    )
    return inspectionsWithUrls
  }
}
