// Initializes the `trailer-sharing` service on path `/trailer-sharing`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Inspection } from './inspection.class'
import createModel from '../../models/inspections.model'
import hooks from './inspection.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'inspection': Inspection & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['create', 'delete']
  }

  // Initialize our service with any options it requires
  app.use('/inspection', new Inspection(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('inspection')

  service.hooks(hooks)
}
