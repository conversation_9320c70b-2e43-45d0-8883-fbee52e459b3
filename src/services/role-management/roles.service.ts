// Initializes the `trailer-sharing` service on path `/trailer-sharing`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Role } from './roles.class'
import createModel from '../../models/roles.model'
import hooks from './roles.hooks'
import authAPI from '../../middleware/api-auth'
import * as helper from '../../helper/helper'
import { TrailerMasterCTR } from '../../interface/etrailer'
import { UserCTR } from '../../interface/user'
import moment from 'moment'
import xlsx from 'json-as-xlsx'
import S3AWS from '../../library/s3AWS'
import { IRole } from '../../interface/role'
import { isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'
import * as Response from '../../helper/response'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'roles': Role & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$regex', '$populate', '$options'],
    multi: ['remove', 'patch']
  }

  // Initialize our service with any options it requires
  app.use('/roles', new Role(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('roles')

  service.publish((data, context) => {
    return app.channel('public')
  })
  service.hooks(hooks)
}
