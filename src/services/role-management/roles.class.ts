import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { IPatchRole, IRole } from '../../interface/role'
import createApplication from '@feathersjs/feathers'
import * as helper from '../../helper/helper'
import _ from 'lodash'

import configs from '../../configs'

export class Role extends Service<IRole> {
  private app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async _create(
    data: Partial<IRole> | Array<Partial<IRole>>,
    params?: createApplication.Params
  ) {
    const roles = Array.isArray(data) ? data : [data]
    return super._create(data, params)
  }

  async _find(params?: any): Promise<createApplication.Paginated<IRole>> {
    try {
      let data = {
        method: 'GET',
        url: helper.getConfigVar('CTRUrl') + '/roles-acl',
        token: await this.app.getCDASToken()
      }
      let { data: ctrRoleAcl } = await helper.request(data)

      const etsRoleAcl = _.map(ctrRoleAcl?.data, ({ name, _id }) => {
        return {
          name,
          ctr_id: _id,
          modules: configs.modules.map(
            ({ moduleName, invisible, additional }) => {
              if (name == 'Administrator') {
                const additionalPermission = additional?.map((name) => {
                  return { [`${name}`]: true }
                })
                return {
                  moduleName,
                  allowCreate: true,
                  allowRead: true,
                  allowUpdate: true,
                  allowDelete: true,
                  invisible,
                  additional: additionalPermission
                }
              }
              const additionalPermission = additional?.map((name) => {
                return { [`${name}`]: false }
              })
              return {
                moduleName,
                invisible,
                additional: additionalPermission
              }
            }
          )
        }
      })

      await Promise.allSettled(
        etsRoleAcl.map(async (role) => {
          return this.Model.updateOne(
            {
              ctr_id: role.ctr_id
            },
            { $setOnInsert: role },
            { upsert: true }
          )
        })
      )

      const existingModules: any = await this.Model.findOne({}).select(
        'modules -_id'
      )

      const existingModuleNames = _.flatMapDeep(existingModules.modules, 'moduleName')

      const newModules = configs.modules.filter(
        (module) => !existingModuleNames.includes(module.moduleName)
      )

      // remove existing modules not in configs
      const modulesToRemove = existingModules.modules.filter(
        (module) => !configs.modules.map((m) => m.moduleName).includes(module.moduleName)
      )

      if (modulesToRemove.length > 0) {
        await this.Model.updateMany(
          {},
          { $pull: { modules: { moduleName: { $in: modulesToRemove.map((m) => m.moduleName) } } } }
        )
      }

      if (newModules.length > 0) {
        await this.Model.updateMany(
          {},
          { $addToSet: { modules: { $each: newModules } } }
        )
      }
    } catch (e) {
      console.error('Get roles error: ', e)
      throw e
    }

    // @ts-ignore
    return super._find(params)
  }

  async _patch(id: string, data: IPatchRole, params?: any) {
    const updateResult = await super._patch(id, data, params)
    this.app.service('roles').emit('patched', updateResult)
    return updateResult
  }
}
