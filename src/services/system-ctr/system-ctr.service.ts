// Initializes the `systemCTR` service on path `/system-ctr`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { SystemCtr } from './system-ctr.class';
import createModel from '../../models/system-ctr.model';
import hooks from './system-ctr.hooks';
import * as helper from '../../helper/helper';
import { uuid } from 'uuidv4';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'system-ctr': SystemCtr & ServiceAddons<any>;
  }
  interface isExist {
    total?: Number;
  }
  interface jwtString {
    data?: any;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/system-ctr', new SystemCtr(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('system-ctr');


  /**
   * @api {get} /system-tms/link-trailer-management get link redirect to TMS side
   * @apiName GetLinkToTMS
   * @apiGroup SystemTMS
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {String} url to TMS.
   */
  // route to get link to TMS system
  app.get('/system-tms/link-trailer-management', async (req:any, res:any) => {
    try {
      let jwt = req.headers.authorization;
      let jwtObject = await helper.request({
        method: 'GET',
        url: app.get('urlAuth'),
        token: jwt
      })
      if (jwtObject.status !== 200) res.status(404).json({message: 'Un authorize'});
      let isExist: any = {};
      let sessionId;
      isExist = await service._find({ query: { jwt } })
      if (isExist.total > 0) sessionId = isExist.data[0].sessionId
      else {
        let tms = await service._create({jwt:jwt, sessionId: uuid(), userId: jwtObject.data.user._id});
        sessionId = tms.sessionId
      }
      //update name of user
      let user: any = await app.service('users').Model.findById(jwtObject.data.user._id).lean();
      let obj = jwtObject.data.user;
      Object.keys(obj).forEach((k) => {
        if (!obj[k]) delete obj[k]
        //obj[k] == null && delete obj[k]
      });
      if (user) {
        await app.service('users').Model.findByIdAndUpdate(jwtObject.data.user._id, obj);
      } else {
        await app.service('users').Model.create(obj);
      }
      //set default trailer monitor config
      await service.setDefaultTrailerMonitor(jwtObject.data.user._id);
      res.json({redirect_url: `${app.get('tmsUrl')}?session_id=${sessionId}`});
    } catch (error: any) {
      res.status(500).json(error.message);
    }
  });

  /**
   * @api {post} /system-tms/jwt get jwt token
   * @apiName GetJWTToken
   * @apiGroup SystemTMS
   * @apiParam {String} sessionId sessionId.
   *
   * @apiSuccess {String} jwt token.
   */
  // route to get link to TMS system
  app.post('/system-tms/jwt', async (req:any, res:any) => {
    try {
      let sessionId = req.body.sessionId;
      let jwtString = await service.Model.findOne({sessionId:sessionId}).lean().exec();
      res.json({token: jwtString});
    } catch (error: any) {
      res.status(500).json(error.message);
    }
  });

  service.hooks(hooks);
}
