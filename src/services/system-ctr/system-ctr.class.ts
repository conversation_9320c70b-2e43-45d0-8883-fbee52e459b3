import { Service, MongooseServiceOptions } from 'feathers-mongoose';
import { Application } from '../../declarations';

export class SystemCtr extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: any;
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options);
    this.app = app;
  }
  async setDefaultTrailerMonitor (userId: string) {
    try {
      let configMonitor: any = await this.app.service('trailer-monitor-config')._find({query:{userId: userId}});
      if (configMonitor.total === 0) await this.app.service('trailer-monitor-config')._create({
        userId: userId,
        config: JSON.stringify(this.app.get('trailerMonitor').defaultSetting)
      })
    }catch (e) {
      throw e;
    }
  }
}
