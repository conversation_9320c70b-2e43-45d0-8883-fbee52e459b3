// Initializes the `system-setting` service on path `/system-setting`
import { ServiceAddons } from '@feathersjs/feathers'
import { Workbook } from 'exceljs'
import * as helper from '../../helper/helper'
import XLSX from 'xlsx'
import { Application } from '../../declarations'
import { SystemSetting } from './system-setting.class'
import createModel from '../../models/system-setting.model'
import hooks from './system-setting.hooks'
import authAPI from '../../middleware/api-auth'
import aws from 'aws-sdk'
import { getConfigVar } from '../../helper/helper'
import moment from 'moment'
import S3AWS from '../../library/s3AWS'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'system-setting': SystemSetting & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['remove']
  }

  const systemSetting = new SystemSetting(options, app)

  // Initialize our service with any options it requires
  app.use('/system-setting', systemSetting)

  // Get our initialized service so that we can register hooks
  const service = app.service('system-setting')

  service.hooks(hooks)

  let s3 = new aws.S3(getConfigVar('awsOptions'))
  // const upload = multer({
  //   storage: multer.diskStorage({
  //     destination: './public/file/sample-trailer',
  //     filename: function(req, file, cb) {
  //       // const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9)
  //
  //       cb(null, file.originalname + '-' + formatDate(Date.now(), '') + '.xlsx')
  //     }
  //   }),
  //   // storage: multerS3({
  //   //   s3: s3,
  //   //   bucket: getConfigVar('aws').bucketName,
  //   //   contentType: multerS3.AUTO_CONTENT_TYPE,
  //   //   metadata: function (req:any, file:any, cb:any) {
  //   //     cb(null, {fieldName: file.fieldname});
  //   //   },
  //   //   key: function (req:any, file:any, cb:any) {
  //   //     let ext = path.extname(file.originalname);
  //   //     cb(null, `${file.originalname}-${Date.now().toString()}${ext}`)
  //   //   }
  //   // }),
  //   fileFilter: function(req, file, callback) {
  //     const mimeType = file.mimetype
  //     console.log(
  //       '📖 ~ file: system-setting.service.ts ~ line 67 ~ mimeType',
  //       mimeType
  //     )
  //     if (
  //       mimeType !==
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' &&
  //       mimeType !== 'application/vnd.ms-excel'
  //     ) {
  //       return callback(null, false)
  //     }
  //     callback(null, true)
  //   }
  // })
  app.post(
    '/system-setting/download-template-file',
    authAPI,
    // upload.array('file', 1),
    async (req: any, res: any) => {

      try {
        // console.log('req.files[0].path', req.files[0].path)
        const result = await readXLSX('public/file/sample-trailer/file-trailer.xlsx')
        if (result[0][1]) {
          Object.keys(result[0][1]).map(key => {
            if (key.includes('Date')) {
              result[0][1][key] = moment(result[0][1][key], 'MM/DD/YY').format('DD/MM/YYYY')
            }
          })
        }

        let companyRequest = {
          url: `${app.get('CTRUrl')}/company`,
          method: 'GET',
          token: req.headers.authorization
        }
        let companies = await helper.request(companyRequest)
        let fileUrl: any = []
        await Promise.all(companies.data.map(async (company: any) => {
          if (req.body?.companyIds && req.body.companyIds.includes(company._id)) {
            fileUrl.push(await writeXLSX(
              [
                { name: 'trailer-sample', data: result[0] },
                { name: 'company', data: [{ company_name: company.name }] },
                {
                  name: 'leasee', data: companies.data.map((item: any) => {
                    return { company_name: item.name }
                  })
                },
                {
                  name: 'trailerStatus',
                  data: [
                    { status: 'Available' },
                    { status: 'UnAvailable' },
                    { status: 'Repair & Maintenance' },
                    { status: 'Allocated' },
                    { status: 'Scrapped' },
                    { status: 'Reserved' }
                  ]
                },
                {
                  name: 'trailerSize',
                  data: [
                    { size: '20\'' }, 
                    { size: '30\'' }, 
                    { size: '40\'' }, 
                    { size: '45\'' },
                    { size: '50\'' }
                  ]
                },
                {
                  name: 'trailerType',
                  data: [
                    { type: 'Side Loader' },
                    { type: 'Skeletal (2x)' },
                    { type: 'Skeletal (3x)' },
                    { type: 'Platform (2-axle)' },
                    { type: 'Platform (Tri-axle)' },
                    { type: 'Bogie Axle' },
                    { type: 'Car Carrier Trailer' },
                    { type: 'Lowbed 15550mm' },
                    { type: 'Lowbed 2-axle' },
                    { type: 'Lowbed Tri-axle' },
                    { type: 'Lowbed Multi-axle' }
                  ]
                },
                {
                  name: 'color',
                  data: [
                    { color: 'Red' },
                    { color: 'Blue' },
                    { color: 'Yellow' },
                    { color: 'Black' },
                    { color: 'Green' },
                    { color: 'Grey' },
                    { color: 'Orange' }
                  ]
                },
                { name: 'option', data: [{ option: 'Y' }, { option: 'N' }] }
              ],
              company.name,
              [
                ['company*', 'company'],
                ['leasee', 'leasee'],
                ['trailerSize*', 'trailerSize'],
                ['trailerType*', 'trailerType'],
                ['trailerStatus*', 'trailerStatus'],
                ['licenceStatus*', 'option'],
                ['onLease', 'option'],
                ['isPairingRequired', 'option'],
                ['billable*', 'option'],
                ['color', 'color']
              ]
            ))
          }
        }))
        return res.json({
          success: true,
          trailerSampleFile: fileUrl
        })
      } catch (err: any) {
        return res.json({
          success: false,
          error: err.message
        })
      }
    }
  )
}

/**
 *
 *
 * @param sheets sheets data [{name, data}]
 * @param filename String
 */
async function writeXLSX(sheets: any, filename: string, columnsSelect: any[]) {
  const key = `${filename}_trailer-file-${moment(new Date()).format('DD-MM-YYYY')}.xlsx`
  const s3 = new S3AWS()
  // const s3Url = await s3.getObjectUrl(key)
  // if (s3Url) return s3Url


  let wb = new Workbook()
  // sheets: [{name: 'trailer-sample', data: [{key: value},{key: value}]},{}]
  for (const sheet of sheets) {
    let ws = wb.addWorksheet(sheet.name)
    if (sheet.name != 'trailer-sample') {
      wb.getWorksheet(sheet.name).state = 'hidden'
    }
    let header = Object.keys(sheet.data[0])
    ws.addRow(header).hidden = true
    for (const data of sheet.data) {
      const keys = Object.keys(data)
      const rowData = keys.map((key) => data[key].toString().trim())
      ws.addRow(rowData)
    }
    if (sheet.name !== 'trailer-sample') continue
    // Format cell is date
    header.map((head, index) => {
      const titleCell = ws.getCell(2, index + 1)
      titleCell.alignment = { vertical: 'middle', horizontal: 'center' }
      titleCell.font = {
        name: 'Calibri',
        family: 4,
        size: 13,
        underline: false,
        bold: true
      }
      if (head.includes('*')) {
        titleCell.font.color = { argb: 'FFFF0000' }
      }
      if (head.includes('Date')) {
        for (let r = 3; r < 100; r++) {
          ws.getCell(r, index + 1).numFmt = 'dd/mm/yyyy'
        }
      }
    })

    // Set sample cell company
    const indexOfCompanyCell = header.indexOf('company*') + 1
    ws.getCell(3, indexOfCompanyCell).value = filename
    // Format (Height, Width) column, row
    ws.getRow(2).height = 42.5
    ws.columns.forEach((column, index) => {
      const value = ws.getCell(2, index + 1).value?.toString().trim() ?? ''
      ws.getColumn(index + 1).width = value.toString() == 'Company' ? 35 : value.toString().length + 4
    })

    // Add validation for cell from 3.
    columnsSelect.map((column) => {
      const index = header.indexOf(column[0])
      if (column[0] && index > -1) {
        for (let i = 3; i < 100; i++) {
          ws.getCell(i, index + 1).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`${column[1]}!$A$2:$A$999`]
          }
        }
      }
    })


  }
  // console.log('writing file : ', filename)
  const now = Math.round(+new Date() / 1000)
  console.log('File not exists in S3 -> Create')

  const buffer = await wb.xlsx.writeBuffer()
  let url = await s3.upload(
    buffer,
    key
  )

  return url
}

function readXLSX(filePath: string) {
  const wb = XLSX.readFile(filePath)
  const result = []
  for (const sheetName of wb.SheetNames) {
    result.push(
      XLSX.utils.sheet_to_json<object>(wb.Sheets[sheetName], {
        defval: '',
        blankrows: true,
        raw: false
      }).map((obj) => {
        const temp = {} as any
        Object.entries(obj).map(([key, value]) => {
          temp[key] = value
        })
        return temp
      })
    )
  }
  return result
}
