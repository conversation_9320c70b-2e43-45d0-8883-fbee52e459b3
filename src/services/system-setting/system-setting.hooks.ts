import { HookContext } from '@feathersjs/feathers';
import job from './../../schedule/cronjob';

export default {
  before: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async (context: Hook<PERSON>ontext) => {
      await job(context.app);
      return context;
    }],
    update: [async (context: HookContext) => {
      await job(context.app)
      return context;
    }],
    patch: [async (context: HookContext) => {
      await job(context.app)
      return context;
    }],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
