import { HookContext } from '@feathersjs/feathers';

export default {
  before: {
    all: [],
    find: [async (context: any) => {
      const query = context.params.query;
      if (context.params.user.company) {
        query.company = context.params.user.company
      }
      context.query = query;
      return context;
    }],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async (context: HookContext) => {
      return context;
    }],
    update: [async (context: HookContext) => {
      return context;
    }],
    patch: [async (context: HookContext) => {
      return context;
    }],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
