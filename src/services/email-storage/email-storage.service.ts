// Initializes the `system-setting` service on path `/system-setting`
import { ServiceAddons } from '@feathersjs/feathers'
import { Workbook } from 'exceljs'
import * as helper from '../../helper/helper'
import XLSX from 'xlsx'
import { Application } from '../../declarations'
import createModel from '../../models/email-storage.model'
import hooks from './email-storage.hooks'
import { EmailStorage } from './email-storage.class'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'email-storage': EmailStorage & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  const emailStorage = new EmailStorage(options, app)



  // Initialize our service with any options it requires
  app.use('/email-storage', emailStorage)

  // Get our initialized service so that we can register hooks
  const service = app.service('email-storage')

  service.hooks(hooks)
}


