import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import * as helper from '../../helper/helper'
import { IRentalRequest } from '@src/interface/rental-request'
import { IRental } from '@src/interface/rental'
import { IBilling } from '@src/interface/billing'

export class EmailStorage extends Service {
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
  }

  async update(id: string, data: any, params?: any): Promise<any> {
    data.updatedBy = params?.user?._id
    // await this.saveHistory(id);
    return super.update(id, data, params)
  }

  async patch(id: string, data: any, params?: any): Promise<any[] | any> {
    data.updatedBy = params?.user?._id
    // await this.saveHistory(id)
    return super.patch(id, data, params)
  }

  async create(data: any, params?: any): Promise<any> {
    console.log('email storage create: ', data)
    return super.create(data)
  }

  [key: string]: any
}
