import { HookContext } from '@feathersjs/feathers';
import job from './../../schedule/cronjob';

export default {
  before: {
    all: [],
    find: [
      async (context: HookContext) => {
        const cpn  = context.params?.user?.company;
        if (cpn) {
          // @ts-ignore
          context.params.query.company = cpn;
        }

        return context;
    }],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async (context: HookContext) => {
      await job(context.app);
      return context;
    }],
    update: [async (context: HookContext) => {
      // await job(app);
      return context;
    }],
    patch: [async (context: HookContext) => {
      await job(context.app);
      return context;
    }],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
