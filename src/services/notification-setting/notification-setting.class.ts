import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { Conflict } from '@feathersjs/errors'

export class NotificationSetting extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
  }

  async update(id: string, data: any, params?: any): Promise<any> {
    data.updatedBy = params?.user?._id
    // await this.saveHistory(id);
    return super.update(id, data, params)
  }

  async patch(id: string, data: any, params?: any): Promise<any[] | any> {
    data.updatedBy = params?.user?._id
    await this.saveHistory(id)
    return super.patch(id, data, params)
  }

  async create(data: any, params?: any): Promise<any> {

    const company = params?.user?.company ? params?.user?.company : data.company
    const notiSetting = await this.Model.findOne({ company }).lean()
    console.log('notiSetting', notiSetting)
    if (notiSetting) {
      throw new Conflict('Only 1 expiry-notification-setting for company.');
    }

    data.createdBy = params?.user?._id
    data.updatedBy = params?.user?._id
    data.company = company


    return super.create(data)
  }

  async saveHistory(id: string){
    const setting = await this.Model.findById(id).lean()
    await this.Model.updateOne({
        $match: {
          id
        }
      },
      {
        $push: {
          history: setting
        }
      })
  }
}
