// Initializes the `system-setting` service on path `/system-setting`
import { ServiceAddons } from '@feathersjs/feathers'
import { Workbook } from 'exceljs'
import * as helper from '../../helper/helper'
import XLSX from 'xlsx'
import { Application } from '../../declarations'
import { NotificationSetting } from './notification-setting.class'
import createModel from '../../models/notification-setting.model'
import hooks from './notification-setting.hooks'

/**
 * @api {post} /notification-settings Create a new notification setting
 * @apiName CreateNotificationSetting
 * @apiGroup NotificationSetting
 * @apiHeader {String} Authorization Token of user
 *
 * @apiParam {String[]} emailReceiver Array of email addresses to receive notifications.
 * @apiParam {Number} [numOfRoadTaxExpiryDate=1] Number of days before road tax expiry to send notification.
 * @apiParam {Number} [numOfInsuranceExpiryDate=1] Number of days before insurance expiry to send notification.
 * @apiParam {Number} [numOfNextInspectionExpiryDate=1] Number of days before next inspection expiry to send notification.
 * @apiParam {Number} [numOfRegistrationExpiryDate=1] Number of days before registration expiry to send notification.
 * @apiParam {String} scheduler Frequency of sending notifications. Can be "Daily", "Weekly", "Fortnightly", or "Monthly".
 *
 * @apiSuccess {Object} data The newly created notification setting.
 */

/**
 * @api {get} /notification-settings/:id Get a specific notification setting
 * @apiName GetNotificationSetting
 * @apiGroup NotificationSetting
 * @apiHeader {String} Authorization Token of user
 *
 * @apiParam {String} id ID of the notification setting to retrieve.
 *
 * @apiSuccess {Object} data The requested notification setting.
 */

/**
 * @api {patch} /notification-settings/:id Update a notification setting
 * @apiName UpdateNotificationSetting
 * @apiGroup NotificationSetting
 * @apiHeader {String} Authorization Token of user
 *
 * @apiParam {String} id ID of the notification setting to update.
 * @apiParam {String[]} [emailReceiver] Array of email addresses to receive notifications.
 * @apiParam {Number} [numOfRoadTaxExpiryDate] Number of days before road tax expiry to send notification.
 * @apiParam {Number} [numOfInsuranceExpiryDate] Number of days before insurance expiry to send notification.
 * @apiParam {Number} [numOfNextInspectionExpiryDate] Number of days before next inspection expiry to send notification.
 * @apiParam {Number} [numOfRegistrationExpiryDate] Number of days before registration expiry to send notification.
 * @apiParam {String} [scheduler] Frequency of sending notifications. Can be "Daily", "Weekly", "Fortnightly", or "Monthly".
 *
 * @apiSuccess {Object} data The updated notification setting.
 */

/**
 * @api {delete} /notification-settings/:id Delete a notification setting
 * @apiName DeleteNotificationSetting
 * @apiGroup NotificationSetting
 * @apiHeader {String} Authorization Token of user
 *
 * @apiParam {String} id ID of the notification setting to delete.
 *
 * @apiSuccess {String} message A success message.
 */

/**
 * @api {get} /notification-settings Get a list of notification settings
 * @apiName GetNotificationSettings
 * @apiGroup NotificationSetting
 * @apiHeader {String} Authorization Token of user
 *
 * @apiSuccess {Object[]} data Array of notification settings.
 */


// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'notification-setting': NotificationSetting & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  const notificationSetting = new NotificationSetting(options, app)



  // Initialize our service with any options it requires
  app.use('/notification-setting', notificationSetting)

  // Get our initialized service so that we can register hooks
  const service = app.service('notification-setting')

  service.hooks(hooks)
}


