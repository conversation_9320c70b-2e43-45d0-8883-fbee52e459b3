import { Application } from '../declarations';
import users from './users/users.service';
import thirdParty from './third-party/third-party.service';
import zoneManagement from './zone-management/zone-management.service';
import trailerAllocation from './trailer-allocation/trailer-allocation.service';
import damageReport from './damage-report/damage-report.service';
import trailerLocation from './trailer-location/trailer-location.service';
import trailerNotification from './trailer-notification/trailer-notification.service';
import trailerAssign from './trailer-assign/trailer-assign.service';
import trips from './trips/trips.service';
import trailers from './trailers/trailers.service';
import systemCtr from './system-ctr/system-ctr.service';
import repairReport from './repair-report/repair-report.service';
import trailerMonitorConfig from './trailer-monitor-config/trailer-monitor-config.service';
import trailerMonitorHistory from './trailer-monitor-history/trailer-monitor-history.service';
import trailerMonitor from './trailer-monitor/trailer-monitor.service';
import images from './images/images.service';
import damageReportAuditLogs from './damage-report-audit-logs/damage-report-audit-logs.service';
import repairReportAuditLogs from './repair-report-audit-logs/repair-report-audit-logs.service';
import trailerMovementHistory from './trailer-movement-history/trailer-movement-history.service';
import trailerSharing from './trailer-sharing/trailer-sharing.service';
import license from './license/license.service';
import systemSetting from './system-setting/system-setting.service';
import notificationSetting from './notification-setting/notification-setting.service';
import emailNotificationSetting from './email-notification-setting/email-notification-setting.service';
import emailStorage from './email-storage/email-storage.service';
import trailerRentalAvailability from './trailer-rental-availability/trailer-rental-availability.service';
import trailerRentalSetting from './trailer-rental-setting/trailer-rental-setting.service';
import rental from './rental/rental.service';
import rentalRequest from './rental-request/rental-request.service';
import joinRequest from './join-request/join-request.service';
import dynamicBlacklist from './dynamic-blacklist/dynamic-blacklist.service';
import inspection from './inspection/inspection.service';
import billing from './billing/billings.service';
import role from './role-management/roles.service';
import cpnRole from './role-cpn-management/cpn-roles.service';
import emailTemplate from './email-template/email-template.service'
import termAndConditions from './sharing-terms-conditions/sharing-terms-conditions.service';
// Don't remove this comment. It's needed to format import lines nicely.

export default function (app: Application): void {
  app.configure(users);
  app.configure(thirdParty);
  app.configure(zoneManagement);
  app.configure(trailerAllocation);
  app.configure(damageReport);
  app.configure(trailerLocation);
  app.configure(trailerNotification);
  app.configure(trailerAssign);
  app.configure(trips);
  app.configure(trailers);
  app.configure(systemCtr);
  app.configure(repairReport);
  app.configure(trailerMonitorConfig);
  app.configure(trailerMonitorHistory);
  app.configure(trailerMonitor);
  app.configure(images);
  app.configure(damageReportAuditLogs);
  app.configure(repairReportAuditLogs);
  app.configure(trailerMovementHistory);
  app.configure(trailerSharing);
  app.configure(license);
  app.configure(systemSetting);
  app.configure(notificationSetting);
  app.configure(emailNotificationSetting);
  app.configure(emailStorage);
  app.configure(trailerRentalAvailability);
  app.configure(trailerRentalSetting);
  app.configure(rental);
  app.configure(rentalRequest);
  app.configure(joinRequest);
  app.configure(inspection);
  app.configure(dynamicBlacklist);
  app.configure(billing);
  app.configure(role);
  app.configure(cpnRole);
  app.configure(emailTemplate)
  app.configure(termAndConditions)
}
