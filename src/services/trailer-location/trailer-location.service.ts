// Initializes the `trailer-location` service on path `/trailer-location`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerLocation } from './trailer-location.class';
import createModel from '../../models/trailer-location.model';
import hooks from './trailer-location.hooks';
import authAPI from "../../middleware/api-auth";
import Redis from "../../library/redis";
import {prefixLatLng} from "../../helper/helper";

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-location': TrailerLocation & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/trailer-location', new TrailerLocation(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-location');

  /**
   * @api {get} /trailer-location-trailer/{trailerNumber} GET BY truckNumber
   * @apiHeader {String} Authorization token of user
   * @apiName Trailer Location get by trailer number
   * @apiGroup TrailerLocation
   *
   * @apiSuccess {Object} location.
   */
  app.get('/trailer-location-trailer/:trailerNumber', authAPI, async (req:any, res:any) => {
    try {
      const redis = new Redis()
      let trailerNumber = req.params.trailerNumber;
      let data:any = await redis.client.get(`${prefixLatLng}${trailerNumber}`);
      let result = data ? JSON.parse(data) : {}
      res.json(result);
    } catch (error: any) {
      res.status(500).json(error.message);
    }
  });

  /**
   * @api {get} /trailer-location-company/{companyId} get by companyId
   * @apiHeader {String} Authorization token of user
   * @apiName Trailer Location get by companyId
   * @apiGroup TrailerLocation
   *
   * @apiSuccess {Object} location.
   */
  app.get('/trailer-location-company/:companyId', authAPI, async (req:any, res:any) => {
    try {
      const redis = new Redis()
      let companyId = req.params.companyId;
      let data:any = await redis.client.get(`${prefixLatLng}${companyId}`);
      let result = data ? JSON.parse(data) : {}
      res.json(result);
    } catch (error: any) {
      res.status(500).json(error.message);
    }
  });


  service.hooks(hooks);
}
