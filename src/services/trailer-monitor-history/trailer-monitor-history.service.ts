// Initializes the `trailer-monitor-history` service on path `/trailer-monitor-history`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { TrailerMonitorHistory } from './trailer-monitor-history.class'
import createModel from '../../models/trailer-monitor-history.model'
import hooks from './trailer-monitor-history.hooks'
import moment from 'moment'
import authAPI from '../../middleware/api-auth'
import S3AWS from '../../library/s3AWS'

let xlsx = require('json-as-xlsx')

const s3 = new S3AWS()

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-monitor-history': TrailerMonitorHistory & ServiceAddons<any>;
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/trailer-monitor-history', new TrailerMonitorHistory(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-monitor-history')

  /**
   * @api {get} /trailer-monitor-history?trailerId={trailerId}
   * @apiName Get list trailer monitor history
   * @apiGroup TrailerMonitorHistory
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {Object} status.
   */

  service.hooks(hooks)

  app.get('/trailer-monitor-history-export-xls/:trailerId', authAPI, async (req: any, res: any) => {
    try {
      let trailerMonitor: any = await service.find({ query: { limit: 10000, trailerId: req.params.trailerId }, headers: req.headers })
      let arrSheet: any[] = []

      await Promise.all(trailerMonitor.data.map(async (item: any) => {
        let dataTrailer = item.trailerNumber

        let row = {
          trailerNo: dataTrailer.trailerNumber,
          company: item.company,
          size: dataTrailer.trailerSize ? dataTrailer.trailerSize.name : null,
          status: dataTrailer.trailerStatus ? dataTrailer.trailerStatus.name : null,
          type: dataTrailer.trailerType ? dataTrailer.trailerType.name : null,
          location: item.location,
          indicator: dataTrailer.color,
          vehicleNo: dataTrailer.truckNumber ? dataTrailer.truckNumber.vehicleNo : null,
          remarks1: item.remarks1 ? item.remarks1 : null,
          remarks2: item.remarks2 ? item.remarks2 : null,
          referenceDateTime1: item.referenceDateTime1 ? moment(item.referenceDateTime1).format('DD-MM-YYYY hh:mm') : null,
          referenceDateTime2: item.referenceDateTime2 ? moment(item.referenceDateTime2).format('DD-MM-YYYY hh:mm') : null,
          updatedAt: item.updatedAt ? moment(item.updatedAt).format('DD-MM-YYYY hh:mm') : null
        }

        if (item.trailerNumber._id == req.params.trailerId) {
          arrSheet.push(row)
        }
      }))
      const data: any[] = [
        {
          sheet: 'TrailerMonitor list',
          columns: [
            { label: 'Trailer No.', value: 'trailerNo' },
            { label: 'Company', value: 'company' },
            { label: 'Size', value: 'size' },
            { label: 'Status', value: 'status' },
            { label: 'Type', value: 'type' },
            { label: 'Location', value: 'location' },
            { label: 'Indicator', value: 'indicator' },
            { label: 'Vehicle No', value: 'vehicleNo' },
            { label: 'Remarks 1', value: 'remarks1' },
            { label: 'Remarks 2', value: 'remarks2' },
            { label: 'Ref Date 1', value: 'referenceDateTime1' },
            { label: 'Ref Date 2', value: 'referenceDateTime2' },
            { label: 'Timestamp', value: 'updatedAt' }
          ],
          content: arrSheet
        }
      ]

      const settings: any = {
        writeOptions: {
          type: 'buffer',
          bookType: 'xlsx'
        }
      }
      const buffer = xlsx(data, settings)
      const now = Math.round(+new Date() / 1000)
      let url = await s3.upload(buffer, `${now}_trailerMonitor${req.params.trailerId}_${moment(new Date()).format('DD-MM-YYYY')}.xlsx`)
      res.json({ url })
    } catch (error: any) {
      res.status(500).json(error.message);
    }
  })
}
