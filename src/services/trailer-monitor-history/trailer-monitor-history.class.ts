import { Service, MongooseServiceOptions } from 'feathers-mongoose';
import { Application } from '../../declarations';
import { prefixRoadNameOf, prefixZoneNameOf } from '../../helper/helper';
import * as helper from '../../helper/helper';
import Redis from '../../library/redis';

export class TrailerMonitorHistory extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options);
    this.app = app;
  }

  async create(data:object) {
    try {
      return await this.Model.create(data);
    }catch (e:any) {
      throw e;
    }
  }

  async find(params: any) {
    const redis = new Redis()
    if (params.user.company) params.query['trailerNumber.company'] = params.user.company;
    const historyMonitor = await super.find(params) as any;

    const zoneService = this.app.service('zone-management')
    await zoneService.getArrCompanies(params.headers.authorization)

    const companiesInRedis = await redis.client.get('companies');
    let companies = companiesInRedis ? JSON.parse(companiesInRedis) : null;


    historyMonitor.data = await Promise.all(historyMonitor.data.map(async (trailer: any) => {

      try {
        // company
        companies.data.map((cpn: any) => {
          if (cpn._id == trailer.trailerNumber.company) trailer.company = cpn.name;
        })

        const zoneNameOf = await redis.client.get(`${prefixZoneNameOf}${trailer.trailerNumber.trailerNumber}`)
        if (zoneNameOf) {
          trailer.location = zoneNameOf;
          return trailer;
        }

        const roadNameOf = await redis.client.get(`${prefixRoadNameOf}${trailer.trailerNumber.trailerNumber}`)
        if (roadNameOf) {
          trailer.location = roadNameOf;
          return trailer;
        }

        const coordinates = JSON.parse(trailer.lastLocation).coordinates;
        const [lng, lat] =  coordinates;
        console.log('Monitor-history [lng, lat]', [lng, lat])

        //Get zone name
        let result = await zoneService.findPointInArrCompanies(Number(lat), Number(lng), trailer.trailerNumber.company, false)
        if (Object.keys(result).length && result.zoneName) {
          trailer.location = 'ZONE_'+result.zoneName
          return trailer
        }
        //Road Name
        let roadNameRequest = {
          method: 'POST',
          url: `${this.app.get('roadUrl')}/road-map/name`,
          token: params.headers.authorization,
          headers: {
            'request-from': 'find trailerMonitorHistory'
          },
          body: {
            latitude: `${lat}`,
            longitude: `${lng}`
          }
        }
        let resultRoadName = await helper.request(roadNameRequest)
        trailer.location = resultRoadName.data?.name ? 'ROAD_'+resultRoadName.data.name : 'ROAD_NOT_FOUND'
      } catch(er) {
        console.log('Monitor-history ERROR: ', er)
        trailer.location = 'NO_LOCATION';
      }
      return trailer;
    }))
    return historyMonitor
  }
  // async find(param:any) {
  //   try {
  //     let query = getQueryConditionHistoryTrailer(param)
  //     return await super.find({
  //       query
  //     })
  //   } catch (e: any) {
  //     throw e;
  //   }
  // }
}
