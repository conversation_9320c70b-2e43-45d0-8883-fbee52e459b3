import { HookContext, HooksObject } from '@feathersjs/feathers'
// import auth from "../../middleware/auth";
import isCDASAdminOrgAdmin from '../../middleware/permission'

export default {
  before: {
    all: [],
    find: [
      async (context: any) => {
        const query = context.params.query
        if (query.username) {
          query.username = { $regex: query.username, $options: 'i' }
        }
        return context
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
} as HooksObject
