// Initializes the `users` service on path `/users`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Users } from './users.class'
import createModel from '../../models/users.model'
import hooks from './users.hooks'
import { isFileAllowed } from '../../middleware/is-upload-to-s3'
import authAPI from '../../middleware/api-auth'
import { checkAccessible } from '../../middleware/permission'
import * as helper from '../../helper/helper'
import _ from 'lodash'
import { sendErrorToWebhook } from '../../helper/helper'
import { NotFound } from '@feathersjs/errors'
import * as Response from '../../helper/response'
import S3AWS from '../../library/s3AWS'
// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    users: Users & ServiceAddons<any>
  }
}

export default function(app: Application): void {

  const options = {
    Model: createModel(app),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
    paginate: app.get('paginate')
  }

  app.get('/users/ets', authAPI, async (req: any, res: any) => {
    try {
      let urlRestrictedUser = `${app.get('CTRUrl')}/usersRestricted`

      // Convert req.query to query parameters
      const queryParams = new URLSearchParams()

      let limit = 0
      console.log('req.query.$limit', req.query.$limit)

      // Check for $limit and set to 200 if greater than 200
      if (req.query.$limit && req.query.$limit > 200) {
        limit = req.query.$limit
        req.query.$limit = 200
      }

      if (req.user.company) {
        queryParams.append('company', req.user.company)
      } else if (req.query.company) {
        queryParams.append('company', req.query.company)
        delete req.query.company
      }

      for (const key in req.query) {
        queryParams.append(key, req.query[key])
      }

      const queryParamString = queryParams.toString()

      // Append the query parameters to the URL
      if (queryParamString) {
        urlRestrictedUser += `?${queryParamString}`
      }

      let data = {
        method: 'GET',
        url: urlRestrictedUser,
        token: req.headers.authorization
      }

      // Make the first request to get the total number of records
      let result = await helper.request(data)
      let totalRecords = result.data.total
      let fetchedRecords = result.data.data

      console.log('fetchedRecords.length', fetchedRecords.length)
      console.log(
        'condition',
        limit,
        fetchedRecords.length < totalRecords && fetchedRecords.length < limit
      )
      // Keep making requests until we've fetched all records or reached the limit
      while (
        fetchedRecords.length < totalRecords &&
        fetchedRecords.length < limit
      ) {
        data.url += `&$skip=${fetchedRecords.length}`
        result = await helper.request(data)
        fetchedRecords.push(...result.data.data)
      }

      result.data.data = fetchedRecords
      Response.success(res, result.data, 200)
    } catch (error: any) {
      Response.error(res, error, error.code)
    }
  })

  app.get(
    '/users/validate-token',
    authAPI,
    checkAccessible,
    async (req: any, res: any) => {
      return res.json({
        accessToken: req.headers.authorization,
        user: req.user
      })
    }
  )


  /**
   * @api {get} /users/{id} get information user
   * @apiName Get User Information
   * @apiGroup Users
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {Object} user.
   */

  // route for forgot password
  app.post('/users/verify-jwt', async (req: any, res: any) => {
    try {
      const user = await service.verifyJWT(req.body.jwt)
      res.json(user)
    } catch (error: any) {
      Response.error(res, error, error.code)
    }
  })

  /**
   * @api {post} /users/upload post image to aws 3
   * @apiName Upload Image
   * @apiGroup DamageReport
   *
   * @apiParam {images} images image of trailer.
   *
   * @apiSuccess {Object} url of public image.
   */

  app.post(
    '/users/upload',
    [authAPI, isFileAllowed(null)],
    async (req: any, res: any) => {
      try {
        if (req.files.length == 0)
          return Response.error(res, 'No file uploaded', 400)
        
        console.log('LOG-req.files', req.files);
        let object = req.files.map((file: any) => {
          return {
            ...file,
            location: file.key
          }
        })

        res.json(object)
      } catch (error: any) {
        Response.error(res, error, error.code)
      }
    }
  )

  app.get(
    '/users/get-s3-url',
    [authAPI],
    async (req: any, res: any) => {
      try {
        const s3 = new S3AWS()
        if (!req.query.key) return Response.error(res, 'Key is required', 400)
        const url = await s3.getUrl(req.query.key, 5 * 60)
        res.json({url})
      } catch (error: any) {
        Response.error(res, error, error.code)
      }
    }
  )

  app.get('/users/driver', [authAPI], async (req: any, res: any) => {
    try {
      const { data: { data: listRoleACL } } = await helper.request({
        method: 'GET',
        url: `${app.get('CTRUrl')}/roles-acl/`,
        token: req.headers.authorization
      });

      const driverRole = listRoleACL.find((role: any) => role.name === 'Transporter - Driver');
      if (!driverRole) {
        throw new NotFound('\'Transporter - Driver\' role not found');
      }
      const aclDriverId = driverRole._id;

      let url = `${app.get('CTRUrl')}/users?roleAcl=${aclDriverId}`;
      const company = req.user.company ? req.user.company : req.query.company
      if (company) url += `&company=${company}`;
      const { data: resultUsers } = await helper.request({
        method: 'GET',
        url,
        token: req.headers.authorization
      });
      const drivers = resultUsers.data;
      delete resultUsers.data;
      if (drivers.length < 1) return res.json({ success: true,...resultUsers, drivers});
      let vehUrl = `${app.get('CTRUrl')}/vehicles?driver[$in][]=${drivers[drivers.length - 1]?._id}`;
      _.map(_.dropRight(drivers), (drv: any) => {
        vehUrl += `&driver[$in][]=${drv._id}`
      })

      const { data: vehicles } = await helper.request({
        method: 'GET',
        url: vehUrl,
        token: req.headers.authorization
      });
      _.map(drivers, (drv) => {
        const ve = _.find(vehicles.data, (v) => {return v.driver == drv._id})
        return _.merge(drv, {vehicleNo: ve?.vehicleNo || ''})
      })

      res.json({ success: true,...resultUsers, drivers});
    } catch (error: any) {
      await sendErrorToWebhook('API get driver', error);
      const errorMessage = error.response?.data?.message ?? error.message;
      Response.error(res, error, error.code)
    }
  });

// Initialize our service with any options it requires
  app.use('/users', new Users(options, app))
  // Get our initialized service so that we can register hooks
  const service = app.service('users')
  service.hooks(hooks)
}
