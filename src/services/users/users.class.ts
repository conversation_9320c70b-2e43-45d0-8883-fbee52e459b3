import { Service, MongooseServiceOptions } from 'feathers-mongoose';
import { Application } from '../../declarations';
import * as Helper  from '../../helper/helper';
import config  from '../../configs/index';
import { Params, Paginated } from '@feathersjs/feathers';

export class Users extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options);
  }

  // function to accommodate user forgot password
  async verifyJWT(jwt:any) {
    let data = {
      url: `${config.ctrUrl}/users`,
      method: 'GET',
      token: `Bearer ${jwt}`
    }
    let result = await Helper.request(data);
    return result.data;
  }
}
