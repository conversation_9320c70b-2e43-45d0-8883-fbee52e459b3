import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { prefixLatLng, prefixRoadNameOf, prefixZoneNameOf } from '../../helper/helper'
import * as helper from '../../helper/helper'
import { Params } from '@feathersjs/feathers'
import { ICreateTrailerRentalSetting, ITrailerRentalSetting } from '../../interface/trailer-rental-setting'

export class TrailerRentalSetting extends Service<ITrailerRentalSetting> {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async create(data: ICreateTrailerRentalSetting, params: Params) {
    if (params.user?.company) {
      data.company = params.user.company
    }

    if (data.company) {
      const setting = await this.Model.findOne({ company: data.company }).lean()
      if (setting) return super._patch(setting._id, data)
    }

    return super.create(data, params)
  }

  async find(params: any) {
    if (params.user && params.user.company) {
      params.query.company = params.user.company
    }
    return super.find(params)
  }
}
