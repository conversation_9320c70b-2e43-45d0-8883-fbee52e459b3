// Initializes the `trailers` service on path `/trailer-rental-availability`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { TrailerRentalSetting } from './trailer-rental-setting.class'
import createModel from '../../models/trailer-rental-setting.model'
import hooks from './trailer-rental-setting.hooks'
import authAPI from '../../middleware/api-auth'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-rental-setting': TrailerRentalSetting & ServiceAddons<any>;
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    lean: true
  }

// Initialize our service with any options it requires
  app.use('/trailer-rental-setting', new TrailerRentalSetting(options, app))
// Get our initialized service so that we can register hooks
  const service = app.service('trailer-rental-setting')
  service.hooks(hooks)
}
