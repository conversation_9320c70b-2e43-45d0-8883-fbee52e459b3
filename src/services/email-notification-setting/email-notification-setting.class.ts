import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'

export class EmailNotificationSetting extends Service {
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
  }

  async update(id: string, data: any, params?: any): Promise<any> {
    data.updatedBy = params?.user?._id
    // await this.saveHistory(id);
    return super.update(id, data, params)
  }

  async patch(id: string, data: any, params?: any): Promise<any[] | any> {
    data.updatedBy = params?.user?._id
    // await this.saveHistory(id)
    return super.patch(id, data, params)
  }

  async create(data: any, params?: any): Promise<any> {

    const company = params?.user?.company ? params?.user?.company : data.company
    const emailSetting = await this.Model.findOne({
      company,
      type: data.type
    }).lean()
    if (emailSetting) {
      throw new Error(`Only 1 ${data.type} for company.`)
    }

    data.createdBy = params?.user?._id
    data.updatedBy = params?.user?._id
    data.company = company


    return super.create(data)
  }

  saveHistory(id: string): any {
    return this.Model.updateOne({
              $match: {
                id
              }
            },
      {
        $push: {
          history: '$$ROOT'
        }
      })
  }

  [key: string]: any
}
