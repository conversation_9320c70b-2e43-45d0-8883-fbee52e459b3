import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { EmailNotificationSetting } from './email-notification-setting.class'
import createModel from '../../models/email-notification-setting.model'
import hooks from './email-notification-setting.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'email-notification-setting': EmailNotificationSetting & ServiceAddons<any>
  }
}

export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['remove']
  }

  const emailNotification = new EmailNotificationSetting(options, app)



  // Initialize our service with any options it requires
  app.use('/email-notification-setting', emailNotification)

  // Get our initialized service so that we can register hooks
  const service = app.service('email-notification-setting')

  service.hooks(hooks)
}


