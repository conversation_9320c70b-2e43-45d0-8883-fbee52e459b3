import { HookContext } from '@feathersjs/feathers';
import * as attachLocation from '../../middleware/attach-location';
export default {
  before: {
    all: [
      // async (context: HookContext) => {
      //   return await auth(context);
    ],
    find: [
      async (context: any) => {
        if (context.params?.user?.company){
          context.params.query.companyId = context.params?.user?.company;
        }
        return context;
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [
      async (context: HookContext) => {
        const trailers = context.result.data;
        const zoneService = context.app.service('zone-management');
        await zoneService.getArrCompanies(context.params?.headers?.authorization)
        context.result.data = await Promise.all(trailers.map((trailer: any) => {
          return attachLocation.toRecord({param: context.params.headers, trailer, zoneService, isCurrentLocation: false})
        }))
        return context;
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
