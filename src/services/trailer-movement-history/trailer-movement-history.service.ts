// Initializes the `trailer-movement-history` service on path `/trailer-movement-history`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerMovementHistory } from './trailer-movement-history.class';
import createModel from '../../models/trailer-movement-history.model';
import hooks from './trailer-movement-history.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-movement-history': TrailerMovementHistory & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/trailer-movement-history', new TrailerMovementHistory(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-movement-history');

  /**
   * @api {get} /trailer-movement-history/ get list
   * @apiHeader {String} Authorization token of user
   * @apiName Trailer Movement History get list
   * @apiGroup TrailerMovementHistory
   *
   * @apiSuccess [Array] list history.
   */

  /**
   * @api {get} /trailer-movement-history/{id} get by id
   * @apiHeader {String} Authorization token of user
   * @apiName Trailer Movement History get detail
   * @apiGroup TrailerMovementHistory
   *
   * @apiSuccess {Object} list history.
   */

  /**
   * @api {get} /trailer-movement-history/?trailerId={trailerId} get by trailer id
   * @apiHeader {String} Authorization token of user
   * @apiName Trailer Movement History get list by trailer id
   * @apiGroup TrailerMovementHistory
   *
   * @apiSuccess {Object} list history.
   */

  service.hooks(hooks);
}
