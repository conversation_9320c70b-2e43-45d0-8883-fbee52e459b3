import { Service, MongooseServiceOptions } from 'feathers-mongoose';
import { Application } from '../../declarations';
import config from "../../configs";
import * as Helper from "../../helper/helper";

export class ThirdParty extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: Application;
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options);
    this.app = app;
  }

  async login(email:string ,password:string, systemName: any) {
    let params = {
      "strategy":"local",
      "email":email,
      "password":password,
      "device":{"os":"computer","FCMId":"portal"},
      "remember":true
    }
    let data = {
      url: `${this.app.get('CTRUrl')}/authentication`,
      method: 'POST',
      body: params
    }
    let result = await Helper.request(data);
    await super.create({
      systemName: systemName
    });
    return result;
  }
}
