// Initializes the `thirdParty` service on path `/third-party`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { ThirdParty } from './third-party.class';
import createModel from '../../models/third-party.model';
import hooks from './third-party.hooks';
import configs from '../../configs';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'third-party': ThirdParty & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/third-party', new ThirdParty(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('third-party');

  /**
   * @api {post} /third-party/login login to MGG via TMS
   * @apiName LoginSSO
   * @apiGroup ThirdParty
   * @apiParam {String} email email.
   * @apiParam {String} password password.
   * @apiParam {String} systemName systemName.
   *
   * @apiSuccess {Object} status.
   */
  //login by CTR account
  app.post('/third-party/login', async (req:any, res:any) => {
    try {
      const user  = await service.login(req.body.email, req.body.password, req.body.systemName);
      res.json(user);
    } catch (error: any) {
      res.status(500).json(error.message);
    }
  });

  // app.get('/static/auth', async (req:any, res:any) => {
  //   try {
  //     res.json({
  //       '_id':'61d3d3c8b00d8255a1feaa12',
  //       'name':'TMS controller',
  //       'email':'<EMAIL>'
  //     });
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });
  //
  // app.get('/static/list-trailer', async (req:any, res:any) => {
  //   try {
  //     res.json(configs.dataListTrailer);
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });
  //
  // app.get('/static/detail-trailer', async (req:any, res:any) => {
  //   try {
  //     res.json(configs.dataDetailTrailer);
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });
  //
  // app.get('/static/create-trailer', async (req:any, res:any) => {
  //   try {
  //     res.json(req.body);
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });
  //
  // app.get('/static/edit-trailer', async (req:any, res:any) => {
  //   try {
  //     res.json(req.body);
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });
  //
  // app.get('/static/job-information', async (req:any, res:any) => {
  //   try {
  //     res.json(configs.dataJob);
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });
  //
  // app.post('/static/connect-job-truck', async (req:any, res:any) => {
  //   try {
  //     res.json({'message':'success'});
  //   } catch ({message}) {
  //     res.status(500).json({message});
  //   }
  // });

  service.hooks(hooks);
}
