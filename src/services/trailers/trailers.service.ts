// Initializes the `trailers` service on path `/trailers`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { Trailers } from './trailers.class'
import hooks from './trailers.hooks'
import * as helper from '../../helper/helper'
import authAPI from '../../middleware/api-auth'

import * as Response from '../../helper/response'
import moment from 'moment'
import S3AWS from '../../library/s3AWS'
import { TrailerMasterCTR } from '../../interface/etrailer'
import { UserCTR } from '../../interface/user'
import _ from 'lodash'
import { BadRequest, Forbidden } from '@feathersjs/errors'
import { DEFAULT_COLOR } from '../../configs/constants'

let xlsx = require('json-as-xlsx')

const s3 = new S3AWS()
const now = Math.round(+new Date() / 1000)

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailers': Trailers & ServiceAddons<any>;
  }
}

export default function(app: Application): void {
  const options = {
    paginate: app.get('paginate'),
    whitelist: ['$populate']
  }

  // Initialize our service with any options it requires
  app.use('/trailers', new Trailers(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trailers')

  service.hooks(hooks)

  /**
   * @api {get} /trailers-export-xls export trailer monitor to exel file
   * @apiName Export trailer  to xlsx file
   * @apiGroup Trailers
   *
   * @apiSuccess {Object} url of link download.
   */


  app.get('/trailers-export-xls', authAPI, async (req: any, res: any) => {
    try {

      let getListUserRequest = {
        method: 'GET',
        url: `${app.get('CTRUrl')}/usersRestricted${
          req.user.company
            ? `?company=${req.user.company}`
            : req.query.company
            ? `?company=${req.query.company}`
            : ''
        }`,
        token: req.headers.authorization
      }
      let result = await helper.request(getListUserRequest)

      const query = {}
      for (const [key, value] of Object.entries(req.query)) {
        if (typeof value === 'object') {
          // @ts-ignore
          for (const [op, date] of Object.entries(value)) {
            // @ts-ignore
            query[`${key}[${op}]`] = date
          }
        } else {
          // @ts-ignore
          query[key] = value
        }
      }
      let trailers: any = await service._find({ token: req.headers.authorization, query: { ...query, limit: 1000 } })

      let arrSheet: any[] = []
      await Promise.all(trailers.data.map(async (item: TrailerMasterCTR) => {

        let createdBy = ''
        if (item.createdBy) {
          const userCreated = result.data.data.find((element: UserCTR) => element._id == item.createdBy)
          createdBy = userCreated ? userCreated.username : ''
        }
        let row = {
          trailerNo: item.trailerNumber,
          company: item.company?.name ? item.company.name : null,
          size: item.trailerSize ? item.trailerSize : null,
          status: item.trailerStatus ? item.trailerStatus : null,
          indicator: item.color ? item.color : null,
          ulwKg: item.ulwKg ? item.ulwKg : null,
          mlwKg: item.mlwKg ? item.mlwKg : null,
          updatedDate: item.updatedAt ? moment(item.updatedAt).format('DD-MM-YYYY') : null,
          createdDate: item.createdAt ? moment(item.createdAt).format('DD-MM-YYYY') : null,
          type: item.trailerType ? item.trailerType : null,
          createdBy,
          remarks: item.remarks,
          attachedVehicle: item.truckNumber?.vehicleNo ? item.truckNumber.vehicleNo : null,
          roadTaxExpiryDate: item.roadTaxExpiryDate ? moment(item.roadTaxExpiryDate).format('DD-MM-YYYY') : null,
          insuranceExpiryDate: item.insuranceExpiryDate ? moment(item.insuranceExpiryDate).format('DD-MM-YYYY') : null,
          registrationDate: item.registrationDate ? moment(item.registrationDate).format('DD-MM-YYYY') : null,
          nextInspectionDate: item.nextInspectionDate ? moment(item.nextInspectionDate).format('DD-MM-YYYY') : null,
          registrationExpiryDate: item.registrationExpiryDate ? moment(item.registrationExpiryDate).format('DD-MM-YYYY') : null
        }
        arrSheet.push(row)
      }))
      const data: any[] = [
        {
          sheet: 'TrailerMaster list',
          columns: [
            { label: 'Trailer No', value: 'trailerNo' },
            { label: 'Company', value: 'company' },
            { label: 'Size', value: 'size' },
            { label: 'Status', value: 'status' },
            { label: 'Indicator', value: 'indicator' },
            { label: 'Type', value: 'type' },
            { label: 'Maximum laden Weight', value: 'mlwKg' },
            { label: 'Unladen Weight', value: 'ulwKg' },
            { label: 'Attached Vehicle', value: 'attachedVehicle' },
            { label: 'Road Tax Expiry', value: 'roadTaxExpiryDate' },
            { label: 'Insurance Expiry', value: 'insuranceExpiryDate' },
            { label: 'Inspection', value: 'nextInspectionDate' },
            { label: 'Registration', value: 'registrationDate' },
            { label: 'Remarks', value: 'remarks' },
            { label: 'Created Date', value: 'createdDate' },
            { label: 'Created By', value: 'createdBy' },
            { label: 'Edited Date', value: 'updatedDate' },
            { label: 'Registration Expiry Date', value: 'registrationExpiryDate' }
          ],
          content: arrSheet
        }
      ]

      const settings: any = {
        writeOptions: {
          type: 'buffer',
          bookType: 'xlsx'
        }
      }
      const buffer = xlsx(data, settings)
      let url = await s3.upload(buffer, `${now}_trailerMaster${moment(new Date()).format('DD-MM-YYYY')}.xlsx`)
      Response.success(res, { url }, 200)
    } catch (error: any) {
      if (error?.response?.data)
        Response.error(res, error.response.data, 500)
      Response.error(res, error, error.code)
    }

  })

  /**
   * @api {post} /trailers/upload post data trailer
   * @apiName UploadTrailers
   * @apiGroup Trailers
   * @apiHeader {String} Authorization token of user
   *
   * @apiParam {Array} data of trailer need to upload.
   *
   * @apiSuccess {Object} status.
   */
  app.post('/trailers/upload', authAPI, async (req: any, res: any) => {
    try {
      let errorMessage = ' Error: '
      let successMessage = ' Success: '
      let errorTrailers = []
      let successTrailers = []
      let message = ''


      try {
        let trailers;
        if (!req.body.length) throw new BadRequest();
        const cpn = req.user.company || req.body[0].company
        if (req.user.company) {
          trailers = await service._find({ token: req.headers.authorization })
        } else {
          trailers = await service._find({ token: req.headers.authorization, query: {company: cpn} })
        }

        const licenseService = app.service('license');

        // Find an available license for the user's company that is valid today
        const license: any = await licenseService.Model.findOne({
          companyId: cpn,
          startDate: { $lt: moment() },
          endDate: { $gte: moment() },
          isAvailable: true
        }).lean();
        if (!license) throw new Forbidden('This company have not license.')
        if (license.trailersLimited < trailers.total + req.body.length) throw new BadRequest('Number of trailer exceeds the limit')

        req.body.forEach((trailer: any) => {
          trailer.color = trailer.color ? trailer.color.toString().toLowerCase() : DEFAULT_COLOR
          if (trailer.indicator) {
            trailer.indicator = trailer.indicator.toString().toLowerCase()
          }
        })
        let requestUpload = {
          method: 'POST',
          url: `${app.get('CTRUrl')}/trailer-master`,
          token: req.headers.authorization,
          body: req.body
        }
        let response = await helper.request(requestUpload)
        if (response.data.failedCreated?.length > 0) {
          const trailerErrors = response.data.failedCreated.map((item: any) => {
            // return `${item.trailerNo} - ${item.error} \n`
            return {trailerNumber: item.trailerNo, error: item.error}
          })
          errorTrailers.push(trailerErrors)
        }
        if (response.data.successfullCreated.length > 0) {
          successTrailers.push(_.map(response.data.successfullCreated, 'trailerNumber'))
          await Promise.all(response.data.successfullCreated.map(async (item: any) => {
            let requestCreate = {
              method: 'POST',
              url: `${app.get('CTRUrl')}/trailer-management`,
              token: req.headers.authorization,
              body: {
                lastLocation: '{"type":"Point","coordinates":[103.8242,1.2607]}',
                trailer: item._id
              }
            }
            let createTrailerMonitor: any = await helper.request(requestCreate)
          }))
        }
      } catch (error: any) {
        message += error.response?.data?.message ? error.response.data.message : error.message
        Response.error(res, { message }, error.code)
      }

      let isSuccess = true
      if (errorTrailers.flat().length > 0) {
        isSuccess = false
        // errorMessage += errorTrailers.flat().join(', ')
        // message += errorMessage
      }
      if (successTrailers.flat().length > 0) {
        successMessage += successTrailers.flat().join(', ')
        message += successMessage
      }

      res.json({ success: isSuccess, message, successTrailers: successTrailers.flat(), errorTrailers: errorTrailers.flat() })

    } catch (error: any) {
      Response.error(res, { message: error.response?.data?.message ? error.response.data.message : error.message }, error.code)
    }
  })

  // Archive-code
  // app.get('/trailers-overview/sharing-trailers', authAPI, async (req: any, res: any) => {
  //   try {
  //     let param = { token: req.headers.authorization, query: req.query }
  //     let result = await service.findSharing(param)
  //     res.json(result)
  //   } catch ({ message }) {
  //     res.status(500).json({ message })
  //   }
  // })

  /**
   * @api {get} /trailers-for-monitor/list-to-add get all trailer to add monitoring
   * @apiName Get list trailers to add monitoring
   * @apiGroup Trailers
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {Object} status.
   */
  app.get('/trailers-for-monitor/list-to-add', authAPI, async (req: any, res: any) => {
    try {
      let param = { token: req.headers.authorization }
      let userRequest = {
        method: 'GET',
        url: app.get('urlAuth'),
        token: param.token
      }
      let resultUser = await helper.request(userRequest)
      let urlTrailerMonitor = `${app.get('CTRUrl')}/trailer-management?$limit=1000&$skip=0`
      let data = {
        method: 'GET',
        url: urlTrailerMonitor,
        token: param.token
      }
      let trailerMonitor = await helper.request(data)
      let trailerMonitorList: any = {
        list: trailerMonitor.data.data,
        user: resultUser.data.user
      }
      let listToAdd = await app.service('trailer-monitor').Model.find({
        createdBy: trailerMonitorList.user._id
      }).lean()
      let arrMonitorId: any[] = []
      listToAdd.map((blackList: any) => {
        arrMonitorId.push(blackList.trailerMonitorId.toString())
      })
      let listMonitor: any[] = []
      trailerMonitorList.list.map((item: any) => {
        if (arrMonitorId.includes(item._id)) listMonitor.push(item)
      })
      let result = {
        total: listMonitor.length,
        limit: 200,
        skip: 0,
        data: listMonitor
      }
      Response.success(res, result, 200)
    } catch (error: any) {
      Response.error(res, error, error.code)
    }
  })

}
