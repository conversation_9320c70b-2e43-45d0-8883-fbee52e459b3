import { Service, MemoryServiceStore } from 'feathers-memory'
import { Application } from '../../declarations'
import { dataLatLong } from '../../static-services/trailer/data'
import * as helper from '../../helper/helper'
import { Params } from '@feathersjs/feathers'
import _ from 'lodash'
import { CTR_REQUEST, CTR_RESPONSE, wait } from '../../helper/helper'
import { TrailerMasterETS } from '../../interface/etrailer'
import { Forbidden, NotFound } from '@feathersjs/errors'
import { DEFAULT_COLOR } from '../../configs/constants'

export class Trailers extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: any

  constructor(options: Partial<MemoryServiceStore>, app: Application) {
    super(options)
    this.app = app
  }

  async getUser(param: any) {
    let userRequest = {
      method: 'GET',
      url: this.app.get('urlAuth'),
      token: param.token || param.headers.authorization
    }
    return await helper.request(userRequest)
  }

  async _find(param: any) {
    try {
      let resultUser = await this.getUser(param)
      let urlTrailerMaster = `${this.app.get(
        'CTRUrl'
      )}/trailer-master?deleted=false`
      if (resultUser.data.user.company) {
        let companyId = resultUser.data.user.company
        let condition: string = `&$or[0][company][$in][]=${companyId}`

        urlTrailerMaster = `${this.app.get(
          'CTRUrl'
        )}/trailer-master?deleted=false${condition}`
      }
      param.query = param.query ?? {}
      param.query.$limit = param.query?.limit || 10
      param.query.$skip = param.query?.skip || 0
      delete param.query?.skip
      delete param.query?.limit

      if (param.query.vehicleNo) {
        urlTrailerMaster += `&vehicleNo=${param.query.vehicleNo.value}`
        delete param.query.vehicleNo
      }

      for (const key in param.query) {
        if (typeof param.query[key] === 'object' && !Array.isArray(param.query[key])) {
          // Handle operator objects like {$gte: 1}
          const queryObj = param.query[key];
          for (const operator in queryObj) {
            urlTrailerMaster += `&${key}[${operator}]=${queryObj[operator]}`;
          }
        } else {
          urlTrailerMaster += `&${key}=${param.query[key]}`;
        }
      }
      console.log(CTR_REQUEST + 'urlTrailerMaster', urlTrailerMaster)
      let data = {
        method: 'GET',
        url: urlTrailerMaster,
        token: param.token || param.headers.authorization
      }
      let trailerMaster = await helper.request(data)
      return trailerMaster?.data
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async find(params: any) {
    return this._find(params)
  }

  async create(data: any, param: Params) {
    try {
      data.color = data.color
        ? data.color.toString().toLowerCase()
        : DEFAULT_COLOR
      if (data?.indicator) {
        data.indicator = data.indicator.toString().toLowerCase()
      }
      let requestCreate = {
        method: 'POST',
        url: `${this.app.get('CTRUrl')}/trailer-master`,
        token: param.token,
        body: data
      }
      let createTrailer = await helper.request(requestCreate)
      let monitorData = {
        lastLocation: '{"type":"Point","coordinates":[103.8242,1.2607]}',
        trailer: createTrailer.data._id
      }
      let monitor = await this.createMonitor(monitorData, param)
      //add relationship to collection
      return createTrailer.data
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async createMonitor(data: any, param: Params) {
    try {
      let requestCreate = {
        method: 'POST',
        url: `${this.app.get('CTRUrl')}/trailer-management`,
        token: param.token,
        body: data
      }
      let createTrailer: any = await helper.request(requestCreate)
      console.log(CTR_RESPONSE + 'createTrailer: ', createTrailer.data)
      return createTrailer.data
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async remove(id: string, param: Params) {
    try {
      //check if trailer isn't mine
      let resultUser = await this.getUser(param)
      if (resultUser.data.user.company) {
        let requestDetail = {
          method: 'GET',
          url: `${this.app.get('CTRUrl')}/trailer-master/${id}`,
          token: param.token
        }
        let detailTrailer = await helper.request(requestDetail)
        let companyId = detailTrailer.data.company._id
        if (resultUser.data.user.company !== companyId)
          throw new Forbidden(
            "You don't have permission to delete this trailer"
          )
      }
      let requestDetailMonitor = {
        method: 'GET',
        url: `${this.app.get('CTRUrl')}/trailer-management?trailer=${id}`,
        token: param.token
      }
      let [err, trailerRelation] = await wait(
        helper.request(requestDetailMonitor)
      )
      if (trailerRelation) {
        let monitorId = trailerRelation.data.data[0]._id
        let requestDeleteMonitor = {
          method: 'DELETE',
          url: `${this.app.get('CTRUrl')}/trailer-management/${monitorId}`,
          token: param.token
        }
        await wait(helper.request(requestDeleteMonitor))
        let isExitBlackList = this.app
          .service('trailer-monitor')
          .Model.findOne({
            trailerId: id
          })
          .lean()
        if (isExitBlackList) {
          await this.app.service('trailer-monitor').Model.remove({
            trailerId: id
          })
        }
      }
      let requestPatchStatus = {
        method: 'PATCH',
        url: `${this.app.get('CTRUrl')}/trailer-master/${id}`,
        body: { licenceStatus: false },
        token: param.token
      }
      let patchTrailer = await helper.request(requestPatchStatus)
      let requestDelete = {
        method: 'DELETE',
        url: `${this.app.get('CTRUrl')}/trailer-master/${id}`,
        token: param.token
      }
      let deleteTrailer = await helper.request(requestDelete)
      return deleteTrailer.data
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async _get(id: string, param: Params) {
    try {
      let requestDetail = {
        method: 'GET',
        url: `${this.app.get('CTRUrl')}/trailer-master/${id}`,
        token: param.token
      }
      let detailTrailer = await helper.request(requestDetail)
      detailTrailer.data.totalDamageReport = await this.app
        .service('damage-report')
        .Model.count({
          trailerId: detailTrailer.data._id
        })
      detailTrailer.data.totalRepairReport = await this.app
        .service('repair-report')
        .Model.count({
          trailerId: detailTrailer.data._id
        })
      return detailTrailer.data
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async getByTrailerNumber(trailerNo: string) {
    try {
      let requestDetail = {
        method: 'GET',
        url: `${this.app.get(
          'CTRUrl'
        )}/trailer-master?trailerNumber=${trailerNo}`,
        token: await this.app.getCDASToken()
      }
      let response = await helper.request(requestDetail)
      if (response.data.data.length === 0)
        throw new NotFound('Trailer not found')
      return response.data.data[0]
    } catch (e: any) {
      if (e.response) throw e.response.data
      throw e
    }
  }

  async get(id: string, params: Params) {
    return this._get(id, params)
  }

  async update(id: string, newData: any, param: Params) {
    try {
      //add when click allocation
      let allocationHistory = {}
      newData.color = newData.color.toLowerCase()
      if (newData.isAllocation) {
        let detail: any = await this.get(id, param)
        let resultUser = await this.getUser(param)
        allocationHistory = {
          trailerId: id,
          onLease: detail.onLease,
          isPairingRequired: detail.isPairingRequired,
          billable: detail.billable,
          trailerNumber: detail.trailerNumber,
          truckNumber: detail.truckNumber,
          pairingDate: new Date(),
          allocatedBy: resultUser.data.user._id,
          createdBy: detail.createdBy,
          updatedBy: detail.updatedBy
        }
        await this.app.service('trailer-allocation')._create(allocationHistory)
      }
      delete newData.isAllocation

      let requestUpdate = {
        method: 'PATCH',
        url: `${this.app.get('CTRUrl')}/trailer-master/${id}`,
        token: param.token,
        body: newData
      }
      let [err, updateTrailer] = await helper.wait(
        helper.request(requestUpdate)
      )
      if (err) throw err
      console.log(CTR_RESPONSE + 'updateTrailer: ', updateTrailer.data)
      return updateTrailer.data
    } catch (e: any) {
      console.log('Error ne: ', e.response)
      if (e.response) throw e.response.data
      throw e
    }
  }

  async getLatLong() {
    /// query data
    return dataLatLong()
  }
}
