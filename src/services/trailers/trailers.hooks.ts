import { HookContext } from '@feathersjs/feathers'
import { UserCTR } from '../../interface/user'
import * as errors from '@feathersjs/errors'
import moment from 'moment'
import { Conflict, Forbidden } from '@feathersjs/errors'
import { authorizeByRole } from '../../middleware/permission'

export default {
  before: {
    all: [
      async (context: HookContext) => {
        ;async (context: any) => {
          try {
            await authorizeByRole(context)
            return context
          } catch (error) {
            throw error
          }
        }
      }
    ],
    find: [],
    get: [],
    create: [async function isExcessTrailersNumber(context: any) {
      const { data } = context
      const { headers } = context.params
      let resultTrailer = await context.app.service('trailers')._find({
        headers: {
          ...headers,
          authorization: await context.app.getCDASToken()
        },
        query: { limit: 1, company: data.company }
      })

      const license: any = await context.app.service('license').Model.findOne({
        companyId: data.company,
        startDate: { $lt: moment() },
        endDate: { $gte: moment() },
        isAvailable: true
      }).lean()

      if (!license) throw new Forbidden('This company have not license.')
      if (!license.trailersLimited) throw new Conflict('Number of trailers limited that are not configured.')
      if (resultTrailer?.total >= license.trailersLimited) throw new Conflict('The number of trailers used by your company exceeded the allowable limit.')
      return context
    }],
    update: [async (context: any) => {
      const user: UserCTR = context.params.user
      try {
        if (!user.company) {
          // User is a CDAS admin, so access is granted
          return context
        }

        const trailerId = context.arguments[0]
        // Check if user has a permission with the action changing billable and licenceStatus in the service
        const trailer = await context.app.service('trailers')._get(trailerId, context.params)

        if (trailer.billable && trailer.billable !== context.data.billable
          || trailer.licenceStatus && trailer.licenceStatus !== context.data.licenceStatus) {
          throw new errors.Forbidden(
            `billable, licence status only editable by CDAS admin`
          )
        }

      } catch (error: any) {
        console.error(`An error occurred while processing the before update hook trailer: ${error.message}`)
        console.error(error.stack)
        throw error
      }
    }],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [async (context: any) => {
      try {
        const trailerRentalAvailabilityService = context.app.service('trailer-rental-availability')
        const trailerRentalAvailability = await trailerRentalAvailabilityService.Model.findOne({ 'trailer._id': context.result._id }).lean()
        if (trailerRentalAvailability) {
          const trailer = await context.app.service('trailers')._get(context.result._id, context.params)
          await trailerRentalAvailabilityService._patch(trailerRentalAvailability._id, { $set: { trailer } })
        }
      } catch (error: any) {
        console.error(`An error occurred while processing the after update hook trailer: ${error.message}`)
        console.error(error.stack)
        throw error
      }
      return context
    }
    ],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
