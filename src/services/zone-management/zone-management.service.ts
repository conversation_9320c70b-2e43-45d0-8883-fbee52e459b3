// Initializes the `zoneManagement` service on path `/zone-management`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { ZoneManagement } from './zone-management.class'
import createModel from '../../models/zone-management.model'
import hooks from './zone-management.hooks'
import validate from '../../helper/validate'
import schema from '../../validation/zone-management'
import authAPI from '../../middleware/api-auth'
import * as helper from '../../helper/helper'
import configs from '../../configs'
import moment from 'moment'
import S3AWS from '../../library/s3AWS'
import Redis from '../../library/redis'
import { prefixLatLng } from '../../helper/helper'
import * as turf from '@turf/turf'
import booleanPointInPolygon from '@turf/boolean-point-in-polygon'
import { Types } from 'mongoose'
// import xlsx from 'json-as-xlsx'
let xlsx = require('json-as-xlsx')

const s3 = new S3AWS()

const now = Math.round(+new Date() / 1000)

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'zone-management': ZoneManagement & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/zone-management', new ZoneManagement(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('zone-management')

  async function getTrailerLocations(
    companyId: string,
    isGetForAdmin: boolean
  ) {
    const redis = new Redis()
    if (isGetForAdmin) {
      const keys = (await redis.client.sendCommand([
        'keys',
        `${prefixLatLng}*`
      ])) as []
      console.log('LOG-keys.length', keys.length)
      const trailerLocation = await Promise.all(
        keys.map(async (key: string) => {
          if (!Types.ObjectId.isValid(key.split('-')[1])) {
            let locationRedis = await redis.client.sendCommand([
              'get',
              `${key}`
            ])
            if (locationRedis) {
              // Convert locationRedis to a string
              const locationString = String(locationRedis)
              // Replace multiple backslashes with a single backslash
              const location = JSON.parse(locationString.replace(/\\\\/g, '\\'))
              return {
                _id: key.split('-')[1],
                location: JSON.parse(location.location)
              }
            }
          }
        })
      )
      // Filter out undefined elements from trailerLocation array
      return trailerLocation.filter((item) => item !== undefined)
    }

    const redisKey = `${prefixLatLng}${companyId}`
    // @ts-ignore
    const redisValue = await redis.client.get(redisKey)
    if (!redisValue) return null
    // Convert locationRedis to a string
    const companyLocationString = String(redisValue)
    // Replace multiple backslashes with a single backslash
    const location = companyLocationString.replace(/\\\\/g, '\\')
    try {
      const data = JSON.parse(location)
      return data
        .filter((item: any) => item.location !== null)
        .map((item: any) => ({
          _id: item.trailerNumber,
          location: JSON.parse(item.location)
        }))
    } catch (e: any) {
      redis.client.del(redisKey)
      console.log('Error del', redisKey)
    }
  }

  /**
   * @api {get} /geofencing/list get list geofencing
   * @apiHeader {String} Authorization token of user
   * @apiName List Geofencing
   * @apiGroup Geofencing
   *
   * @apiSuccess {Array} list zone.
   */
  app.get('/geofencing/list', authAPI, async (req: any, res: any) => {
    try {
      let filter = {}
      let company = req.user.company
      let zone = await service.Model.find(filter).lean().exec()

      try {
        zone = JSON.parse(JSON.stringify(zone))
        // get list trailer monitor
        const trailerMonitors = await app.service('trailer-monitor')._find({
          query: { limit: 1000 },
          token: await app.getCDASToken()
        })

        const trailerLocationAdmin = await getTrailerLocations('', true)
        zone = await Promise.all(
          zone.map(async (zoneElement: any) => {
            let count = 0
            let trailers: any[] = []
            try {
              let trailerLocation = trailerLocationAdmin
              if (!zoneElement.isAdminZone) {
                trailerLocation = await getTrailerLocations(
                  zoneElement.companyId,
                  false
                )
              }

              console.log('Data: ', trailerLocation, zoneElement)

              const polygon = turf.polygon(zoneElement.geometry.coordinates)

              const trailersInZone = trailerLocation.filter((trailer: any) => {
                const point = turf.point([
                  Number(trailer.location.coordinates[0]),
                  Number(trailer.location.coordinates[1])
                ])
                return booleanPointInPolygon(point, polygon)
              })

              count = trailersInZone.length

              trailers = trailersInZone
                .map((trailer: any) => {
                  const trailerMonitor = trailerMonitors.data.find(
                    (item: any) =>
                      item.trailerNumber.trailerNumber === trailer._id
                  )
                  return trailerMonitor ? trailerMonitor.trailerNumber : null
                })
                .filter((trailer: any) => trailer !== null)
            } catch (e: any) {
              console.log(
                'Count trailer err with zone ' + zoneElement.name,
                e.message
              )
            }
            return {
              ...zoneElement,
              numOfTrailer: count,
              trailers: trailers
            }
          })
        )
      } catch (er) {
        console.log('error count location', er)
      }

      res.json(zone)
    } catch (error: any) {
      console.log('error', error)
      const { message } = error
      res.status(500).json({ message })
    }
  })

  /**
   * @api {get} /geofencing/export-xls link to download xls file
   * @apiHeader {String} Authorization token of user
   * @apiName Download Geofencing
   * @apiGroup Geofencing
   *
   * @apiSuccess {Object} url to download.
   */
  app.get('/geofencing/export-xls', authAPI, async (req: any, res: any) => {
    try {
      let queryData = {} as any
      if (req.user.company) queryData.companyId = req.user.company
      console.log('queryData', queryData)
      let zone = await service.Model.find(queryData).lean().exec()
      console.log('zone', zone.length)
      let arrSheet: any[] = []
      let companyRequest = {
        url: `${app.get('CTRUrl')}/company`,
        method: 'GET',
        token: req.headers.authorization
      }
      let companies = await helper.request(companyRequest)
      await Promise.all(
        zone.map(async (item: any) => {
          let companyName = null
          companies.data.map((company: any) => {
            if (
              item.companyId &&
              item.companyId.toString() === company._id.toString()
            )
              companyName = company.name
          })
          let user: any = await app
            .service('users')
            .Model.findById(item.createdBy)
            .lean()
          let row = {
            companyId: item.companyId ? item.companyId.toString() : null,
            companyName: companyName,
            zoneName: item.zoneName,
            isAdminZone: item.isAdminZone,
            createdBy: item.createdBy.toString(),
            createdByName: user.fullname,
            status:
              configs.statusZone.active === item.status ? 'active' : 'inactive',
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }
          arrSheet.push(row)
        })
      )
      const data: any[] = [
        {
          sheet: 'GeoFencing list',
          columns: [
            // { label: "companyId", value: "companyId" },
            { label: 'Company', value: 'companyName' },
            { label: 'Geofence Name', value: 'zoneName' },
            // { label: "isAdminZone", value: "isAdminZone" },
            { label: 'Status', value: 'status' },
            // { label: "createdBy", value: "createdBy" },
            { label: 'Created By', value: 'createdByName' },
            { label: 'Created At', value: 'createdAt', format: 'd-mmm-yy' },
            { label: 'Updated At', value: 'updatedAt', format: 'd-mmm-yy' }
          ],
          content: arrSheet
        }
      ]

      const settings: any = {
        writeOptions: {
          type: 'buffer',
          bookType: 'xlsx'
        }
      }
      const buffer = xlsx(data, settings)
      let url = await s3.upload(
        buffer,
        `${now}_geofencing${moment(new Date()).format('DD-MM-YYYY')}.xlsx`
      )
      res.json({ url })
    } catch (error: any) {
      res.status(500).json(error.message)
    }
  })

  function countTrailerInZone(zone: any, condition: string, req: any) {
    return zone
  }

  /**
   * @api {post} /zone-management/lat-lng get zone name by lat long
   * @apiHeader {String} Authorization token of user
   * @apiName Get Zone Name by lat lng
   * @apiGroup ZoneManagement
   * @apiParam {String} latitude latitude of trailer .
   * @apiParam {String} longitude longitude of trailer .
   *
   * @apiSuccess {Object} zone name object.
   */
  app.post('/zone-management/lat-lng', authAPI, async (req: any, res: any) => {
    try {
      await validate(schema.latLng, req.body)
      let { latitude, longitude, companyId } = req.body
      let result = await (
        await service.getArrCompanies(req.headers.authorization)
      ).findPointInArrCompanies(
        Number(latitude),
        Number(longitude),
        companyId,
        true
      )
      res.json(result)
    } catch (error: any) {
      res.status(500).json(error.message)
    }
  })

  /**
   * @api {patch} /zone-management/{id}
   * @apiHeader {String} Authorization token of user
   * @apiName Get Zone Name by lat lng
   * @apiGroup ZoneManagement
   * @apiParam {Number} status status of zone 0 for active 1 for inactive.
   *
   * @apiSuccess {Object} zone name object.
   */

  service.hooks(hooks)
}
