import {HookContext, HooksObject} from '@feathersjs/feathers';
import Redis from "../../library/redis";
import {prefixZone,prefixZoneAdmin} from "../../helper/helper";
import { authorizeBy<PERSON><PERSON> } from '../../middleware/permission'

export default {
  before: {
    all: [
      async (context: any) => {
        try {
          await authorizeByRole(context)
          return context
        } catch (error) {
          throw error
        }
      }
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [
      async (context: HookContext) => {
        const redis = new Redis()
        let key = context.result.companyId
          ? `${prefixZone}${context.result.companyId}-${context.result._id}`
          : `${prefixZoneAdmin}${context.result._id}`
        //await redis.client.rPush(key, JSON.stringify(context.result));
        await redis.client.set(key, JSON.stringify(context.result))
        // let a = await redis.client.keys('location-*');
        //
        // console.log(a);
      }
    ],
    update: [
      async (context: HookContext) => {
        const redis = new Redis()
        let key = context.result.companyId
          ? `${prefixZone}${context.result.companyId}-${context.result._id}`
          : `${prefixZoneAdmin}${context.result._id}`
        await redis.client.del(key)
        await redis.client.set(key, JSON.stringify(context.result))
      }
    ],
    patch: [],
    remove: [
      async (context: HookContext) => {
        const redis = new Redis()
        let key = context.result.companyId
          ? `${prefixZone}${context.result.companyId}-${context.result._id}`
          : `${prefixZoneAdmin}${context.result._id}`
        await redis.client.del(key)
      }
    ]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
} as HooksObject
