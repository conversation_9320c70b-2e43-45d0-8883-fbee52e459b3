import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { Params } from '@feathersjs/feathers'
import * as helper from '../../helper/helper'
import * as errors from '@feathersjs/errors'
import * as turf from '@turf/turf'
import config from '../../configs'
import Redis from '../../library/redis'
import { prefixZone, prefixZoneAdmin } from '../../helper/helper'
import lskGeoFence from '../../library/lskGeoFence'
import booleanPointInPolygon from '@turf/boolean-point-in-polygon'

const geofence = new lskGeoFence()

export class ZoneManagement extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  private app: any
  private userDetail: any
  private userRole: any
  private arrCompanies: any[]

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.arrCompanies = []
  }

  async init(param: Params) {
    let userRequest = {
      method: 'GET',
      url: this.app.get('urlAuth'),
      token: param.token
    }
    this.userDetail = await helper.request(userRequest)
    let userRoleRequest = {
      method: 'GET',
      url: `${this.app.get('CTRUrl')}/roles-acl/${this.userDetail.data.user.roleAcl}`,
      token: param.token
    }
    this.userRole = await helper.request(userRoleRequest)
  }

  async getArrCompanies(token: string) {
    const redis = new Redis()

    let companyRequest = {
      url: `${this.app.get('CTRUrl')}/company`,
      method: 'GET',
      token: token
    }
    let companiesInRedis = await redis.client.get('companies')
    let result = companiesInRedis ? JSON.parse(companiesInRedis) : null
    if (!result) {
      result = await helper.request(companyRequest)
      await redis.client.set('companies', JSON.stringify({ data: result.data }), { EX: 60 * 60 })
    }
    this.arrCompanies = result.data
    return this
  }

  async findPointInArrCompanies(latitude: number, longitude: number, companyId: string, isCDAS: boolean) {
    const redis = new Redis()
    let result: any = {}
    let isDone = false
    let zoneKeys: any[] = await redis.client.keys(`${prefixZone}${companyId}*`)

    if (zoneKeys.length > 0) {
      let zone = await this.turfPointInZone(zoneKeys, latitude, longitude)
      if (zone) {
        result = zone;
        isDone = true;
      }
    }

    if (isCDAS && !isDone) {
      let zoneKeysAdmin: any[] = await redis.client.keys(`${prefixZoneAdmin}*`)
      if (zoneKeysAdmin.length > 0) {
        let zone = await this.turfPointInZone(zoneKeysAdmin, latitude, longitude)
        if (zone) result = zone
      }
    }
    return result
  }

  async getZoneContain(zoneKeys: any[], latitude: any, longitude: any) {
    const redis = new Redis()
    let result: any = null
    await Promise.all(zoneKeys.map(async (key: string) => {
      let zone: any = await redis.client.get(key)
      let zoneObj = JSON.parse(zone)
      //console.log(zoneObj);
      let coordinates = zoneObj.geometry.coordinates[0]
      coordinates = coordinates.slice(0, -1)
      let loc = {
        //location info
        locID: 'est_1',
        label: 'name',
        //reference pt
        //lon : 0.0,
        //lat : 0.0,
        lon: Number(longitude),
        lat: Number(latitude),
        rad: 5000,         //in meters, if within this radius means nearby
        tolerance: 50,      //in meters, hysteresis margin from fence, for checking if a pt is inside or outside
        tolerance2: -20,
        fenceArrIdx: -1
      }
      let ptArr: any[] = []
      coordinates.map((point: any) => {
        ptArr.push({ lon: point[0], lat: point[1] })
      })
      let myLon = Number(longitude) + 0.005
      let myLat = Number(latitude) + 0.005
      let locIdx = 0
      geofence.addLocation(loc)
      let fence = geofence.createGeofence(ptArr, 0)
      if (fence && geofence.isInside(locIdx, fence, myLon, myLat)) result = zoneObj
    }))
    return result
  }

  async turfPointInZone(zoneKeys: any[], latitude: any, longitude: any) {
    const redis = new Redis()
    let result: any = null
    await Promise.all(zoneKeys.map(async (key: string) => {
      let zone: any = await redis.client.get(key)
      zone = zone ? zone.replaceAll('\\\\\"', '\\\"') : zone
      let zoneObj = JSON.parse(zone)
      if (zoneObj.status !== config.statusZone.active) {
        return result
      }
      let coordinates = zoneObj.geometry.coordinates
      let poly = turf.polygon(coordinates)
      let pt = turf.point([Number(longitude), Number(latitude)])
      let isInside = booleanPointInPolygon(pt, poly)
      if (isInside) result = zoneObj
    }))
    return result
  }

  getPolygon(zoneInstance: any) {
    let polygon = JSON.parse(zoneInstance)
    let lastPoint = polygon.geometry.coordinates[0][0]
    let geometry = polygon.geometry
    let arrPoint = polygon.geometry.coordinates[0]
    arrPoint.push(lastPoint)
    polygon.geometry.coordinates[0] = arrPoint
    return geometry
  }

  // function to accommodate user forgot password
  async create(data: any, param: Params) {
    await this.init(param)
    let isAdminZone = false
    if (this.userRole.data.forCdas) isAdminZone = true
    let geometry = this.getPolygon(data.zoneInstance)
    let dataInsert: any = {
      zoneName: data.zoneName,
      zoneInstance: data.zoneInstance,
      geometry: geometry,
      isAdminZone: isAdminZone,
      createdBy: this.userDetail.data.user._id
    }
    if (this.userDetail.data.user.company) dataInsert.companyId = this.userDetail.data.user.company
    return await super.create(dataInsert)
  }

  async update(id: string, data: any, param: Params) {
    if (data.zoneInstance) data.geometry = this.getPolygon(data.zoneInstance)
    return await super.update(id, data, param)
  }

  async find(param: Params) {
    await this.init(param)
    let query: any = {
      $limit: 1000,
      status: config.statusZone.active,

    }
    // if (this.userRole.data.forCdas) query = { status: config.statusZone.active }
    if (this.userDetail.data.user.company) query.companyId = this.userDetail.data.user.company
    return await super.find({ query })
  }

  async remove(id: string, param: Params) {
    const redis = new Redis()
    await this.init(param)
    let zone: any = await this.Model.findById(id).lean()
    if (!this.userRole.data.forCdas) {
      if (zone.isAdminZone) throw new errors.GeneralError(' You don\'t have permission to delete')
      if (zone.companyId.toString() !== this.userDetail.data.user.company.toString()) throw new errors.GeneralError(' You don\'t have permission to delete')
    }
    let key = zone.companyId ? `${prefixZone}${zone.companyId}-${zone._id}` : `${prefixZoneAdmin}${zone._id}`
    await redis.client.del(key)
    return await super.remove(id)
  }

}
