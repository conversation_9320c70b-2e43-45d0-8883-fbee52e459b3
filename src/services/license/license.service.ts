// Initializes the `license` service on path `/license`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { License } from './license.class';
import createModel from '../../models/license.model';
import hooks from './license.hooks';
import authAPI from '../../middleware/api-auth'
import { authorizeByRole } from '../../middleware/permission'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    license: License & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/license', new License(options, app))

  app.post(
    '/license/add-members',
    authAPI,
    authorizeByRole,
    async (req: any, res: any) => {
      try {
        const { _id, users } = req.body

        const license = await app.service('license')._patch(_id, { users })
        res.json(license)
      } catch (error: any) {
        res.status(500).json({ message: error.message })
      }
    }
  )

  // Get our initialized service so that we can register hooks
  const service = app.service('license')

  service.hooks(hooks)
}
