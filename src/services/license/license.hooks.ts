import { HookContext, HooksObject } from '@feathersjs/feathers'
import isCDASAdminOrgAdmin, { authorizeByRole } from '../../middleware/permission'
// import auth from '../../middleware/auth';

export default {
  before: {
    all: [
      // async (context: HookContext) => {
      //   return await auth(context);
      // }

      async (context: any) => {
        try {
          await authorizeByRole(context)
          return context
        } catch (error) {
          throw error
        }
      }
    ],
    find: [],
    get: [],
    create: [
      async (context: HookContext) => {
        const createData = context.data
        if (
          createData.licenseLimit <
          createData.users.filter(
            (user: any) =>
              user.allowAccess == true || user.allowAccess == undefined
          ).length
        ) {
          throw new Error(
            'Numbers of users added should be less or equal "No. of Users Allocated" '
          )
        }
        return context
      }
    ],
    update: [isCDASAdminOrgAdmin],
    patch: [isCDASAdminOrgAdmin],
    remove: []
  },

  after: {
    all: [],
    find: [
      async function countCurrentTrailer(context: any) {
        const { result } = context
        const { headers } = context.params

        await Promise.all(
          result.data.map(async (license: any) => {
            try {
              const trailersResponse = await context.app
                .service('trailers')
                ._find({
                  headers,
                  query: { company: license.companyId }
                })
              license.currentTrailers = trailersResponse.total
            } catch (e) {
              license.currentTrailers = 'unknown'
            }
          })
        )
        return context
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
