import { Params } from '@feathersjs/feathers'
import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import moment from 'moment'
import { Application } from '../../declarations'

export class License extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
  }

  create(data: any, params?: any): Promise<any> {
    data.createdBy = params.user._id
    return super._create(data)
  }

  async find(params: any) {
    const isCDASAdmin = !params.user.company
    if (isCDASAdmin) {
      return await super._find(params)
    }
    params.query = {
      companyId: params.user.company,
      startDate: { $lt: moment() },
      endDate: { $gte: moment() },
      isAvailable: true,
      ...params.query
    }
    return await super._find(params)
  }
}
