// Initializes the `trailer-notification` service on path `/trailer-notification`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { TrailerNotification } from './trailer-notification.class';
import createModel from '../../models/trailer-notification.model';
import hooks from './trailer-notification.hooks';

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'trailer-notification': TrailerNotification & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
  };

  // Initialize our service with any options it requires
  app.use('/trailer-notification', new TrailerNotification(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-notification');

  service.publish((data, context) => {
    const channel = `Company-Notification-${data.companyId}`;
    return app.channel(channel)
  })

  service.hooks(hooks);

  /**
   * @api {get} /trailer-notification get list notification
   * @apiName ListNotification
   * @apiGroup Notification
   * @apiHeader {String} Authorization token of user
   *
   * @apiSuccess {Object} data of repair report.
   */
}
