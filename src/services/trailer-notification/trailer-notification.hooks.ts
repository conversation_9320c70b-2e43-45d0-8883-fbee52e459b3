import { HooksObject } from '@feathersjs/feathers';

export default {
  before: {
    all: [],
    find: [
      async (context: any) => {
        const query = context.params.query;
        if (query.trailerNo) {
          query.trailerNo = { $regex: new RegExp(query.trailerNo)}
        }
        if (context.params.user.company) {
          query.companyId = context.params.user.company
        }
        context.query = query;
        return context;
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
