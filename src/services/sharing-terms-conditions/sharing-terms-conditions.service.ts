// Initializes the `trailer-sharing` service on path `/trailer-sharing`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { SharingTermsConditions } from './sharing-terms-conditions.class'
import createModel from '../../models/sharing-terms-conditions.model'
import hooks from './sharing-terms-conditions.hooks'
import authAPI from '../../middleware/api-auth'
import { isFileAllowed, uploadFiles } from '../../middleware/is-upload-to-s3'
import * as Response from '../../helper/response'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'sharing-terms-conditions': SharingTermsConditions & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['create']
  }

  app.post(
    '/sharing-terms-conditions/upload',
    authAPI,
    // isFileAllowed('sharing-terms-conditions'),
    uploadFiles({
      folder: 'sharing-terms-conditions',
    }),
    async (req: any, res: any) => {
      try {
        let object = req.files
        if (object.length === 0) {
          throw new Error('No file uploaded')
        }

        const data = {
          terms_conditions_key: `${object[0].key}`,
          company: req.user.company
        }
        const existingRecord = await app.service('sharing-terms-conditions')._find({
          query: {
            company: req.user.company,
          },
          paginate: false
        })
        let result
        if (Array.isArray(existingRecord) && existingRecord.length > 0) {
          result = await app.service('sharing-terms-conditions')._patch(
            existingRecord[0]._id,
            data
          )
        } else {
          result = await app.service('sharing-terms-conditions')._create(data)
        }
        Response.success(res, result)
      } catch (error: any) {
        Response.error(res, error, error.code)
      }
    }
  )
  // Initialize our service with any options it requires
  app.use('/sharing-terms-conditions', new SharingTermsConditions(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('sharing-terms-conditions')

  service.hooks(hooks)
}
