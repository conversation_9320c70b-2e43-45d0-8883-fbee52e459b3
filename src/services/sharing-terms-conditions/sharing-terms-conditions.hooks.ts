export default {
  before: {
    all: [],
    find: [
      async (context: any) => {
        const query = context.params.query
        if (!query.company && context.params.user.company) {
          query.company = context.params.user.company
        }
        context.query = query
        return context
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [
      async (context: any) => {
        const data = context.result.data
        if (context.params.user.company) {
          context.result = data[0] || null
        }
        return context
      }
    ],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
