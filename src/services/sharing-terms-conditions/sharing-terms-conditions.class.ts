import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { ISharingTermsConditions } from '../../interface/term-and-conditions'

export class SharingTermsConditions extends Service<ISharingTermsConditions> {
  private app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
}
