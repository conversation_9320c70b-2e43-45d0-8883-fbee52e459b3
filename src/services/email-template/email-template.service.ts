import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { EmailTemplate } from './email-template.class'
import createModel from '../../models/email-template.model'
import hooks from './email-template.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'email-template': EmailTemplate & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  const emailNotification = new EmailTemplate(options, app)

  // Initialize our service with any options it requires
  app.use('/email-template', emailNotification)

  // Get our initialized service so that we can register hooks
  const service = app.service('email-template')

  service.hooks(hooks)
}
