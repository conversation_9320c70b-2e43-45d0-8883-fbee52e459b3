import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { IRentalRequest } from '@src/interface/rental-request'
import { IRental } from '@src/interface/rental'
import { IBilling } from '@src/interface/billing'

export class EmailTemplate extends Service {
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
  }

  
}
