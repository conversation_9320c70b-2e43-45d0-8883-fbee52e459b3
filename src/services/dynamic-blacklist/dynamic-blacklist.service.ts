import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { DynamicBlacklist } from './dynamic-blacklist.class'
import hooks from './dynamic-blacklist.hooks'
import createModel from '../../models/dynamic-blacklist.model'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'dynamic-blacklist': DynamicBlacklist & ServiceAddons<any>;
  }
}


export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/dynamic-blacklist', new DynamicBlacklist(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('dynamic-blacklist')

  service.hooks(hooks)
}
