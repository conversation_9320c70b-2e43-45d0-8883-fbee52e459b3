import { HooksObject, HookContext } from '@feathersjs/feathers'
import { BadRequest } from '@feathersjs/errors'
import { CTVJoinRequest } from '../../models/join-request.model'

export default {
  before: {
    all: [
      async (context: HookContext) => {
        // console.log(
        //   '/-----------/-----------/-----------/-----------/-----------/-----------/-----------'
        // )
        // console.log('header', context.params)
      }
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],

    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
