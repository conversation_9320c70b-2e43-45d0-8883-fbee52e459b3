// Initializes the `trailers` service on path `/trailer-rental-availability`
import { ServiceAddons } from '@feathersjs/feathers'
import { Application } from '../../declarations'
import { RentalRequest } from './rental-request.class'
import hooks from './rental-request.hooks'
import createModel from '../../models/rental-request.model'
import authAPI from '../../middleware/api-auth'
import * as Response  from '../../helper/response'
import { isAccessibleDMS } from '../../middleware/permission-in-dynamic-sharing'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'rental-request': RentalRequest & ServiceAddons<any>;
  }
}



export default function(app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['remove'],
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
  }


  app.get('/rental-request/outgoing',authAPI, isAccessibleDMS, async (req: any, res: any) => {
    const { query, user, headers } = req;
    const params = {
      headers,
      query,
      user
    };
    const company = user.company ? user.company : query?.company
    if (company) params.query["requester.company"] = company
    try {
      const result = await service.find(params);
      res.json(result);
    } catch (error: any) {
      console.error(`Error while retrieving outgoing rental requests: ${error.message}`);
      console.error(error.stack);
      Response.error(res, error, error.code)
    }
  })

  app.get('/rental-request/incoming',authAPI, isAccessibleDMS, async (req: any, res: any) => {
    const { query, user, headers } = req;
    const params = {
      headers,
      query,
      user
    };
    const company = user.company ? user.company : query?.company
    if (company) params.query["owner.company"] = company
    try {
      const result = await service.find(params);
      res.json(result);
    } catch (error: any) {
      console.error(`Error while retrieving incoming rental requests: ${error.message}`);
      console.error(error.stack);
      Response.error(res, error, error.code)
    }
  })

// Initialize our service with any options it requires
  app.use('/rental-request', new RentalRequest(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('rental-request')
  service.hooks(hooks)
}
