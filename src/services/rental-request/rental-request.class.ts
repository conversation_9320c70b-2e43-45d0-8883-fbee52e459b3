import { Service, MongooseServiceOptions } from 'feathers-mongoose'
import { Application } from '../../declarations'
import { BadRequest, Conflict, Forbidden } from '@feathersjs/errors'
import mongoose from 'mongoose'
import { CTVRentalRequest } from '../../models/rental-request.model'
import { Id, Params } from '@feathersjs/feathers'
import { CTVRental } from '../../models/rentals.model'
import { appHelper, sendErrorToWebhook } from '../../helper/helper'
import * as helper from '../../helper/helper'
import _ from 'lodash'
import {
  IPatchTrailerRentalAvailability,
  ITrailerRentalAvailability
} from '../../interface/trailer-rental-availability'
import { ICreateRental, IRental } from '../../interface/rental'
import { ICreateRentalRequest, IPatchRentalRequest, IRentalRequest } from '../../interface/rental-request'

export class RentalRequest extends Service<IRentalRequest> {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async create(createData: ICreateRentalRequest, params?: any) {
    const data: IRentalRequest = <IRentalRequest>createData
    const { trailerRentalId, requestStart, requestEnd, requestType, rentalId, assignee } = data

    data.requester = params.user.company ? {
      company: params.user.company,
      name: params.user.fullname,
      email: params.user.email
    } : data.requester
    await this.isAvailableForNewRental(data)
    const trailerForRent = await this.app.service('trailer-rental-availability')._get(trailerRentalId as Id)
    // Check available date of the trailer-availability

    // const trailerRentalAvailability = await this.app.service('trailer-rental-availability').get(trailerRentalId)
    if (requestType == CTVRentalRequest.type.Extension) {
      // if (!trailerRentalAvailability.isLeasing) throw new BadRequest('This trailer is not leased. Can\'t make extension request')
      const rental = await this.app.service('rental')._get(rentalId as Id)
      data.requestStart = rental.end
      if (!assignee) data.assignee = rental.assignee
    }
    try {

      data.rentingDetails = trailerForRent.rentingDetails
      data.owner = {
        name: trailerForRent.trailer?.company?.name,
        email: trailerForRent.trailer?.company?.groupInfo?.email,
        company: trailerForRent.trailer?.company?._id
      }
      data.trailerNumber = trailerForRent.trailer.trailerNumber
      data.estimatedAmount = this.calEstimateAmount(data)
      data.numberOfDays = this.countNumberOfDays(data)
      data.updatedBy = params?.user?._id
      data.createdBy = params?.user?._id

      return super.create(data, params)
    } catch (error) {
      await sendErrorToWebhook('Error create rental request', { error })
      throw error
    }
  }

  async patch(id: string, data: IPatchRentalRequest, params?: any) {
    let result
    data.updatedBy = params?.user?._id
    const originalRequest = await super.get(id)
    if (params.user.company &&
      (params.user.company !== originalRequest.owner.company.toString()
        && params.user.company !== originalRequest.requester.company.toString()))
      throw new Forbidden('You do not have permission to access this resource')

    if (originalRequest.status !== CTVRentalRequest.status.Pending && data.status !== originalRequest.status)
      throw new Conflict('Can\'t update this request')


    if (originalRequest.status !== data.status && data.status == CTVRentalRequest.status.Approved) {
      const updateRequest = { ...originalRequest, ...data }
      result = await this.approveRequest({ id, request: updateRequest, params, originalRequest })
    } else {
      result = await super.patch(id, data, params)
    }
    return result
  }

  async approveRequest({
                         id,
                         request,
                         params,
                         originalRequest
                       }: { id: string, request: IRentalRequest, params: any, originalRequest: IRentalRequest }) {

    try {
      if (request.requestType == CTVRentalRequest.type.New) await this.isAvailableForNewRental(request)
      const estimatedAmount = this.calEstimateAmount(request)
      const numberOfDays = this.countNumberOfDays(request)
      const trailerForRent = await this.app.service('trailer-rental-availability')._get(String(request.trailerRentalId))

      let rentalPayload: ICreateRental = {
        assignee: request.assignee,
        trailerRentalId: request.trailerRentalId,
        trailerId: trailerForRent.trailer?._id,
        trailerNumber: request.trailerNumber,
        rentee: request.requester,
        owner: {
          name: trailerForRent.trailer?.company?.name,
          email: trailerForRent.trailer?.company?.groupInfo?.email,
          company: trailerForRent.trailer?.company?._id
        },
        start: request.requestStart,
        end: request.requestEnd,
        rentingDetails: request.rentingDetails,
        numberOfDays,
        estimatedAmount,
        requestId: request._id as Id,
        status: CTVRental.status.NotStarted
      }

      switch (request.requestType) {
        case CTVRentalRequest.type.New: {
          const now = new Date()
          if (request.requestStart > now) rentalPayload.status = CTVRental.status.UpComing
          rentalPayload.inspections = []
          const rental = await this.app.service('rental')._create(rentalPayload) as IRental

          // Add lease information to trailerCTR
          await this.updateTrailerIsRented({
            rental,
            request,
            trailerForRent,
            params
          })
          break
        }
        case CTVRentalRequest.type.Extension: {
          const extendFromRental = await this.app.service('rental')._get(request.rentalId as Id)

          rentalPayload.inspections = extendFromRental.inspections
          const newRental = await this.app
            .service('rental')
            .singleCreate(rentalPayload)

          await this.app
            .service('trailer-rental-availability')
            .patch(String(request.trailerRentalId), {
              $push: { rentalHistory: newRental._id }
            })
          break
        }
        case CTVRentalRequest.type.ChangeRequest: {
          console.log('request.rentalId', request.rentalId)
          await this.app.service('rental')._patch(request.rentalId as Id, {
            start: request.requestStart,
            end: request.requestEnd
          }, params)
          break
        }
        default:
          break
      }

      return await super.patch(id, request, params)
    } catch (error: any) {
      await sendErrorToWebhook('approveRequest error', { request, error: error.message })
      throw error
    }

  }

  async updateTrailerIsRented({
                                request,
                                trailerForRent,
                                rental,
                                params
                              }: { request: IRentalRequest, trailerForRent: ITrailerRentalAvailability, rental: IRental, params: any }) {
    try {
      let updateTRA = {
        $push: { rentalHistory: rental._id }
      }
      if (request?.requestStart.getTime() < new Date().getTime()) {
        updateTRA = _.merge(updateTRA, {
          isLeasing: true,
          rental: rental._id
        })
      }
      // Add renting information to trailer-rental-availability
      await this.app.service('trailer-rental-availability')._patch(request.trailerRentalId as Id, updateTRA, params)

      // @ts-ignore
      const cdasToken = await this.app.getCDASToken()

      let requestUpdate = {
        method: 'PATCH',
        url: `${this.app.get('CTRUrl')}/trailer-master/${trailerForRent.trailer?._id}`,
        token: cdasToken,
        body: { leasee: request.requester.company, onLease: true }
      }
      console.log('requestUpdate', requestUpdate)
      const result = await helper.request(requestUpdate)
    } catch (error: any) {
      await sendErrorToWebhook('updateTrailerIsRented', error.message)
      throw error
    }
  }

  async isAvailableForNewRental(request: ICreateRentalRequest) {
    const { trailerRentalId, requestStart, requestEnd, rentalId } = request
    try {
      const trailerForRent = await this.app.service('trailer-rental-availability')._get(trailerRentalId.toString())
      if (trailerForRent && !trailerForRent.isAvailable)
        throw new BadRequest('The trailer is not available for rent')

      const token = await this.app.getCDASToken()
      let getQuest = {
        method: 'GET',
        url: `${this.app.get('CTRUrl')}/trailer-master?deleted=false&_id=${trailerForRent.trailer?._id}`,
        token
      }
      const result = await helper.request(getQuest)
      if (!result.data.total) throw new BadRequest('The trailer is not available for rent.')

      const exist = await this.app.service('rental').getProcessingRental(trailerRentalId.toString(), requestStart, requestEnd, rentalId?.toString())
      if (exist) throw new BadRequest('The trailer is not available for lease at this period.')
    } catch (error: any) {
      console.error(`An error occurred in isAvailableForNewRental: ${error.message}`)
      console.error(error.stack)
      throw error
    }

  }

  async get(id: Id, params?: any) {
    const request = await super.get(id, {
      ...params,
      query: {
        $populate: [{ path: 'trailerRentalId', select: ['availableForRentTo'] }]
      }
    })
    return Object.assign(request, { activeRentals: await this.app.service('rental').getActiveRental(request.trailerRentalId as Id) })
  }

  calEstimateAmount(request: ICreateRentalRequest) {
    const { requestStart, requestEnd, rentingDetails, requestType } = request
    const diffInDays = this.countNumberOfDays(request)
    return diffInDays * rentingDetails.dailyPrice
  }

  countNumberOfDays(request: ICreateRentalRequest) {
    const { requestStart, requestEnd, rentingDetails, requestType } = request
    const diffInDays = Math.round((new Date(requestEnd).getTime() - new Date(requestStart).getTime()) / (1000 * 60 * 60 * 24))
    if (diffInDays < 3 && requestType == CTVRentalRequest.type.New) {
      throw new BadRequest('The minimum rental period is 3 days. Please choose a rental period of at least 3 days.')
    }
    return diffInDays
  }

  async find(params: Params) {
    return super.find(params)
  }
}
