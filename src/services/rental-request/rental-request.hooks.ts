import { HooksObject, HookContext } from '@feathersjs/feathers'
import { BadRequest } from '@feathersjs/errors'
import { CTVRentalRequest } from '../../models/rental-request.model'

export default {
  before: {
    all: [
      async (context: HookContext) => {
        // console.log(
        //   '/-----------/-----------/-----------/-----------/-----------/-----------/-----------'
        // )
        // console.log('header', context.params)
      }
    ],
    find: [
      async (context: any) => {
        const query = context.params.query
        if (query.trailerNo) {
          query.trailerNumber = { $regex: query.trailerNo, $options: 'i' }
        }
        if (query.trailerNumber) {
          query.trailerNumber = { $regex: query.trailerNumber, $options: 'i' }
        }
        if (query.message) {
          query.message = { $regex: query.message, $options: 'i' }
        }
        if (query['rentingDetails.dailyPrice']) {
          query['rentingDetails.dailyPrice'] =
            +query['rentingDetails.dailyPrice']
        }
        context.query = query
        return context
      }
    ],
    get: [],
    create: [
      async function isTrailerAvailableForRent(context: any) {
        const { data } = context
        const { trailerRentalId, requestType, rentalId } = data
        if (!trailerRentalId || !requestType)
          throw new BadRequest('Bad request')

        // if (trailerForRent && trailerForRent.isLeasing && requestType == CTVRentalRequest.type.New) {
        //   throw new BadRequest('This trailer is leased')
        // }

        // if (data.type == CTVRentalRequest.type.Extension) {
        //   const rental = await context.app.service('rental').getProcessingRental(trailerRentalId, data.requestStart, data.requestEnd);
        //   if (rental) throw new BadRequest('The trailer is in an upcoming booking')
        // }

        const rental = await context.app
          .service('rental')
          .getProcessingRental(
            trailerRentalId,
            data.requestStart,
            data.requestEnd,
            rentalId?.toString()
          )
        if (rental) {
          throw new BadRequest(
            'The trailer is not available for lease at this period.'
          )
        }

        return context
      }
    ],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [
      async function checkAutoAcceptExtension(context: any) {
        try {
          const { result } = context
          if (result.requestType == CTVRentalRequest.type.Extension) {
            const rentalSetting = await context.app
              .service('trailer-rental-setting')
              .Model.findOne({
                company: result.owner.company,
                autoAcceptList: {
                  $in: [result.requester.company]
                }
              })
              .lean()
            if (rentalSetting) {
              await context.app
                .service('rental-request')
                ._patch(result._id, {
                  status: CTVRentalRequest.status.Approved
                })
            }
          }
        } catch (e: any) {
          console.error(
            'Error in check auto accept extension request',
            e.message
          )
          console.error(e.stack)
        }
        return context
      }
    ],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
