import {HookContext, HooksObject} from '@feathersjs/feathers';
import auth from "../../middleware/auth";
import configs from "../../configs";
import _ from "lodash";
import moment from "moment";
import * as errors from "@feathersjs/errors";

const startOfMonth = moment().startOf('month').format('YYYY-MM-DD hh:mm');
const endOfMonth   = moment().endOf('month').format('YYYY-MM-DD hh:mm');

const addImage = async (context: any) => {
  let arrData:any[];
  arrData = [];
  context.data.images.map((item: string) => {
    arrData.push({
      repairReportId: context.result._id,
      url: item
    })
  });
  await context.app.service('images')._create(arrData);
  return context;
}
export default {
  before: {
    all: [
      async (context: HookContext) => {
        return await auth(context);
      }
    ],
    find: [],
    get: [],
    create: [
      async (context: any) => {
        // if (!context.data.damageReportId) throw new errors.Unprocessable('Select damage report Id to create');
        context.data.createdBy = context.params.user._id;
        context.data.submittedBy = context.params.user._id;
        context.data.status = configs.statusDamageReport.draft;
        let lastOfMonth = await context.app.service('repair-report').Model.findOne({
          createdAt: {
            $gte: startOfMonth,
            $lte: endOfMonth
          }
        }).sort({createdAt: -1}).lean();
        let lastRRR = '001';
        if (lastOfMonth){
          let lastRRRNum = parseInt(lastOfMonth.reportId.slice(lastOfMonth.reportId.toString().length - 3)) + 1;
          let prefix = '00';
          if (lastRRRNum > 10) prefix = '0';
          if (lastRRRNum > 100 ) prefix = '';
          lastRRR = `${prefix}${lastRRRNum.toString()}`
        }
        context.data.reportId = `${context.data.reportId}${lastRRR}`;
      }
    ],
    update: [],
    patch: [
      async (context: HookContext) => {
        //save history
        let detail = await context.app.service('repair-report').Model.findById(context.id).lean();
        //check status
        if (context.data.status === configs.statusDamageReport.void){
          let arrStatus = [configs.statusDamageReport.approved,configs.statusDamageReport.closed]
          if (arrStatus.includes(detail.status)) {
            throw new errors.Forbidden('You cannot void this report');
          }
        }
        detail.repairReportId = detail._id;
        delete detail._id;
        delete detail.createdAt;
        delete detail.updatedAt;
        let arrImage = await context.app.service('images').Model.find({repairReportId: context.id}).lean();
        detail.imageArrId = _.map(arrImage, "_id")
        await context.app.service('repair-report-audit-logs').Model.create(detail);
        //end save history
      }

    ],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [
      async (context: HookContext) => {
        let images = context.result.arrImages = await context.app.service('images')._find({
          query: {repairReportId: context.result._id}
        });
        context.result.arrImages = images.data;
        return context;
      }
    ],
    create: [
      async (context: HookContext) => {
        await context.app.service('damage-report').Model.update(
          // { reportId: context.result.damageReportId },
          { lastRepairReportId: context.result._id }
        );
        return addImage(context);
      }
    ],
    update: [],
    patch: [
      async (context: HookContext) => {
        return addImage(context);
      }
    ],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
