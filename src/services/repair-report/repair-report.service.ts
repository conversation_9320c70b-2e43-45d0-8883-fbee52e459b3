// Initializes the `repair-report` service on path `/repair-report`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../declarations';
import { RepairReport } from './repair-report.class';
import createModel from '../../models/repair-report.model';
import hooks from './repair-report.hooks';
import authAPI from "../../middleware/api-auth";
import * as helper from "../../helper/helper";
import configs from "../../configs";
let xlsx = require("json-as-xlsx");
import moment from 'moment';
import S3AWS from "../../library/s3AWS";
import * as Response from '../../helper/response'
const s3 = new S3AWS();
const now = Math.round(+new Date()/1000);

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'repair-report': RepairReport & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/repair-report', new RepairReport(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('repair-report');

  app.get('/repair-report-export-xls',authAPI, async (req:any, res:any) => {
    let reports = await service.Model.find();
    let arrSheet: any[] = [];
    let companyRequest = {
      url: `${app.get('CTRUrl')}/company`,
      method: 'GET',
      token: req.headers.authorization
    }
    let companies = await helper.request(companyRequest);
    await Promise.all(reports.map( async (item:any) => {
      let companyName = null;
      companies.data.map((company:any) => {
        if (item.companyId && (item.companyId.toString() === company._id.toString())) companyName = company.name;
      });
      let status = helper.getKeyByValue(configs.statusDamageReport,item.status);
      let row = {
        reportId: item.reportId,
        damageReportId: item.damageReportId,
        trailerNo: item.trailerNo,
        companyName: companyName,
        status : status,
      }
      arrSheet.push(row);
    }))

    const data: any[] = [
      {
        sheet: "Repair Report list",
        columns: [
          { label: "repairReport", value: "reportId" },
          { label: "damageReport", value: "damageReportId" },
          { label: "trailerNo", value: "trailerNo" },
          { label: "companyName", value: "companyName" },
          { label: "status", value: "status" }
        ],
        content: arrSheet,
      }
    ]

    const settings: any = {
      writeOptions: {
        type: "buffer",
        bookType: "xlsx",
      },
    }
    const buffer = xlsx(data, settings);
    let url = await s3.upload(buffer, `${now}_repairReport${moment(new Date()).format("DD-MM-YYYY")}.xlsx`);
    Response.success(res, {url}, 200)
  })

  service.hooks(hooks);
}
