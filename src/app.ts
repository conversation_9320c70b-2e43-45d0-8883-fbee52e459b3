// @ts-nocheck

import path from 'path'
import favicon from 'serve-favicon'
import compress from 'compression'
import helmet from 'helmet'
import cors from 'cors'
import morgan from 'morgan'

import feathers from '@feathersjs/feathers'
import configuration from '@feathersjs/configuration'
import express from '@feathersjs/express'
import socketio from '@feathersjs/socketio'
import sync from 'feathers-sync'

import { Application } from './declarations'
const app: Application = express(feathers())

app.configure(configuration())
import { setConnectUrl } from './library/redis'
setConnectUrl(app.get('redis'))

import { getConfigVar } from './helper/helper'

import logger from './logger'
import middleware from './middleware'
import services from './services'
import appHooks from './app.hooks'
import channels from './channels'
import { HookContext as FeathersHookContext } from '@feathersjs/feathers'
import mongoose from './our-mongoose'
import { notFound, errorHandler } from '@feathersjs/express'

//cronjob back up date every day
import job from './schedule/cronjob'
import * as helper from './helper/helper'
import { appHelper, onStartUp } from './helper/helper'
import { DEFAULT_PAGING } from './configs/constants'

// Don't remove this comment. It's needed to format import lines nicely.


app.use(morgan('dev'))
app.use((req, res, next) => {
  const limit = parseInt(req.query.$limit)
  if (!req.url.includes('/users/ets'))
    req.query.$limit = isNaN(limit)
      ? DEFAULT_PAGING.LIMIT
      : Math.min(limit, DEFAULT_PAGING.MAX_LIMIT)
  next()
})
// const swaggerOptions = {
//   definition: {
//     openapi: '3.0.0',
//     info: {
//       title: 'ETS API documentation',
//       version: '1.0.0',
//       description: ''
//     },
//     'securityDefinitions':
//       { 'jwt': { 'type': 'apiKey', 'in': 'header', 'name': 'Authorization' } },
//     'security': [{ 'jwt': [] }],
//     components: {
//       ...components
//     },
//     servers: [
//       {
//         url: 'http://localhost:3030',
//         description: 'Development server'
//       }
//     ],
//     paths
//   },
//   // Path to the API docs
//   apis: ['./service/*.ts']
// }

// Initialize swagger-jsdoc -> returns validated swagger spec in json format
// const swaggerSpec = swaggerJSDoc(swaggerOptions)

// Add the Swagger UI middleware
// app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec))

export type HookContext<T = any> = {
  app: Application
} & FeathersHookContext<T>

// Load app configuration
// Enable security, CORS, compression, favicon and body parsing
app.use(
  helmet({
    contentSecurityPolicy: false
  })
)
// app.use((req, res, next) => {
//   res.header("Access-Control-Allow-Origin", "*");
//   res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
//   next();
// });
console.log('cors origins', app.get('cors').origin)
app.use(cors({ origin: app.get('cors').origin, credentials: true }))
app.use(compress())
app.use(express.json({ limit: '1mb' }))
app.use(express.urlencoded({ extended: true }))
app.use(favicon(path.join(app.get('public'), 'favicon.ico')))
// Host the public folder
app.use('/', express.static(app.get('public')))
//mount document
// app.use(express.static(path.join(app.get('public'), '/docs')))

// Set up Plugins and providers
app.configure(express.rest())
app.configure(
  sync({
    uri: getConfigVar('redis')
  })
)

app.configure(
  socketio((io) => {
    io.use((socket, next) => {
      // @ts-ignore
      socket.feathers.token = socket.handshake.query.token.trim()
      //console.log('handshake', socket.feathers.token = socket.handshake.query.token)
      next()
    })
    io.on('connection', function (socket) {
      // console.log('handshake', socket.handshake)
    })
  })
)

app.configure(mongoose)

// Configure other middleware (see `middleware/index.ts`)
app.configure(middleware)

// Set up our services (see `services/index.ts`)
app.configure(services)

app.use(notFound({ verbose: true }))

app.configure(job)

//app.configure(staticServices);
// Set up event channels (see channels.ts)
app.configure(channels)

// Configure a middleware for 404s and the error handler
app.use(express.errorHandler({ logger } as any))

// Add any new real-time connection to the `everybody` channel
app.on('connection', async (connection) => {
  try {

    if (!connection.token) throw Error('Connection without token')

    let userRequest = {
      method: 'GET',
      url: app.get('urlAuth'),
      token: connection.token
    }
    const user = await helper.request(userRequest)
    if (!user.data?.user?.company) throw Error('User have no company')
    const channel = `Company-Notification-${user.data.user.company}`;
    console.log('Channel', channel)
    app.channel(channel).join(connection)

  } catch (e: any) {
    console.error(e.message)
  }
  app.channel('everybody').join(connection)
})


// Publish all events to the `everybody` channel
app.publish((data) => app.channel('everybody'))

app.hooks(appHooks)
onStartUp(app);
appHelper(app);
export default app
