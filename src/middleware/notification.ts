import { IBilling } from '@src/interface/billing'
import { IRental } from '@src/interface/rental'
import { IRentalRequest } from '@src/interface/rental-request'
import * as helper from '../helper/helper'
import { IInspection } from '@src/interface/inspection'
import { format } from 'date-fns'

const RequestTrailer = `<Customer Company Name> has requested to lease your trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-out#request)`
const CancelRequest = `<Customer Company Name> has canceled their request to lease your trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-out#request)`
const AcceptRequest = `<Owner Company Name> has approved your request to lease you their trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-in#live)`
const RejectRequest = `<Owner Company Name> has rejected your request to lease you their trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-in#request)`
const CancelLiveLease = `<Owner Company Name> has canceled your lease for their trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-out#live)`
const RejectLiveLease = `<Customer Company Name> has rejected the lease for your trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-out#live)`
const StartLiveLease = `<Customer Company Name> has picked up your trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-out#live)`
const EndLiveLease = `<Customer Company Name> has returned your trailer <Plate #> - <Size> <Type>. View here. (https://etrailer.cdaslink.sg/lease-out#live)`
const CompleteLiveLease = `<Owner Company Name> has checked your returned trailer <Plate #> - <Size> <Type> and completed the lease. View here. (https://etrailer.cdaslink.sg/lease-in#live)`
const SetBillingStatusClosed = `<Owner Company Name> has set the billing status for leasing Trailer <Plate #> - <Size> <Type> on <Duration Date> as "Closed". View here. (https://etrailer.cdaslink.sg/lease-in#bill)`
const SetBillingStatusPending = `<Owner Company Name> has set the billing status for leasing Trailer <Plate #> - <Size> <Type> on <Duration Date> as "Pending". View here. (https://etrailer.cdaslink.sg/lease-in#bill)`
const SetBillingStatusWaived = `<Owner Company Name> has set the billing status for leasing Trailer <Plate #> - <Size> <Type> on <Duration Date> as "Waived". View here. (https://etrailer.cdaslink.sg/lease-in#bill)`

const notificationMap = {
  'renting-request': { message: RequestTrailer, subject: 'Request Trailer!' },
  'cancel-request': { message: CancelRequest, subject: 'Cancel Request!' },
  'accept-request': { message: AcceptRequest, subject: 'Accept Request!' },
  'reject-request': { message: RejectRequest, subject: 'Reject Request!' },
  'cancel-live-lease': {
    message: CancelLiveLease,
    subject: 'Cancel Live Lease!'
  },
  'reject-live-lease': {
    message: RejectLiveLease,
    subject: 'Reject Live Lease!'
  },
  'start-live-lease': { message: StartLiveLease, subject: 'Start Live Lease!' },
  'end-live-lease': { message: EndLiveLease, subject: 'End Live Lease!' },
  'complete-live-lease': {
    message: CompleteLiveLease,
    subject: 'Complete Live Lease!'
  },
  'set-billing-status-closed': {
    message: SetBillingStatusClosed,
    subject: 'Set Billing Status Closed!'
  },
  'set-billing-status-pending': {
    message: SetBillingStatusPending,
    subject: 'Set Billing Status Pending!'
  },
  'set-billing-status-waived': {
    message: SetBillingStatusWaived,
    subject: 'Set Billing Status Waived!'
  }
}

export async function sendSharingNotification(context: any) {
  try {
    const action: string = transformContextToAction(context)
    const { receiverCpn, senderCpn, trailerNo } = await transformContextResult(
      context
    )
    const notification = notificationMap[action]

    if (notification) {
      let { message, subject } = notification

      const trailer = await context.app
        .service('trailers')
        .getByTrailerNumber(trailerNo)
      let request, liveLease, billing, inspection
      if (context.path === 'rental-request') {
        request = context.result
      } else if (context.path === 'rental') {
        liveLease = context.result
      } else if (context.path === 'billings') {
        billing = context.result
      } else if (context.path === 'inspection') {
        inspection = context.result
      }
      message = formatMessage({
        template: message,
        trailer,
        request,
        inspection,
        liveLease,
        billing
      })
      pushNotification(context.app, {
        trailerNo,
        receiverCpn,
        senderCpn,
        subject,
        message,
        action
      })
    }
  } catch (e: any) {
    console.log('sendNotification ERROR:', e.message)
  }
}

async function pushNotification(
  app,
  { trailerNo, receiverCpn, senderCpn, subject, message, action }
) {
  const notiData = {
    trailerNo,
    vehicleNo: '',
    companyId: receiverCpn.company,
    action,
    message
  }
  try {
    await app.service('trailer-notification').Model.create(notiData)
    app.service('trailer-notification').emit('created', notiData)

    const emailSetting = await app
      .service('email-notification-setting')
      .Model.findOne({
        company: receiverCpn.company,
        isEnable: true,
        type: 'sharing-notification'
      })

    let receiver = receiverCpn.email
    if (emailSetting) {
      receiver = emailSetting.emailReceiver.join(',')
    }
    await app.sendCustomNotification({
      type: 'email',
      subject,
      receiver,
      typeStorage: action,
      companyId: senderCpn.company.toString(),
      content: message
    })
  } catch (e: any) {
    console.log('pushNotification ERROR:', e.message)
  }
}

function formatMessage({
  template,
  trailer,
  request,
  liveLease,
  inspection,
  billing
}: {
  template: any
  trailer?: any
  request?: IRentalRequest
  inspection?: IInspection
  liveLease?: IRental
  billing?: IBilling
}) {
  const templateContent = template

  let html = templateContent.replaceAll(
    '<Customer Company Name>',
    request?.requester?.name ||
      liveLease?.rentee?.name ||
      inspection?.rentee?.name ||
      billing?.rental?.rentee?.name
  )
  const url = html.match(/\(([^)]+)\)/)[1]

  html = html.replaceAll(
    '<Owner Company Name>',
    request?.owner?.name ||
      liveLease?.owner?.name ||
      inspection?.owner?.name ||
      billing?.rental?.owner?.name
  )
  html = html.replaceAll(
    '<Plate #>',
    liveLease?.trailerNumber ||
      request?.trailerNumber ||
      inspection?.trailerNumber ||
      billing?.rental?.trailerNumber
  )
  html = html.replaceAll('<Size>', trailer?.trailerSize || 'N/A')
  html = html.replaceAll('<Type>', trailer?.trailerType || 'N/A')
  html = html.replaceAll(
    '<Duration Date>',
    `${format(billing?.rental?.start, 'dd/MM/yyyy')} - ${format(
      billing?.rental?.end,
      'dd/MM/yyyy'
    )}` || 'N/A'
  )

  // Replace the word "here" with an HTML anchor tag that includes the URL
  html = html.replace('here.', `<a href="${url}">here</a>.`)
  html = html.replace(`(${url})`, '')
  return html
}

const actionMap = {
  'post:rental-request:Pending': 'renting-request',
  'patch:rental-request:Canceled': 'cancel-request',
  'patch:rental-request:Approved': 'accept-request',
  'patch:rental-request:Rejected': 'reject-request',
  'patch:rental:CanceledByOwner': 'cancel-live-lease',
  'patch:rental:RejectedByCustomer': 'reject-live-lease',
  'patch:rental:Completed': 'complete-live-lease',
  'patch:billings:Closed': 'set-billing-status-closed',
  'patch:billings:Pending': 'set-billing-status-pending',
  'patch:billings:Waived': 'set-billing-status-waived',
  'post:inspection:TakeOver': 'start-live-lease',
  'post:inspection:HandOver': 'end-live-lease'
}

function transformContextToAction(context: any) {
  const { path, data, result } = context
  let method = context.method
  if (method === 'create') {
    method = 'post'
  } else if (method === 'update') {
    method = 'patch'
  }
  const key = `${method}:${path}:${result.status || result.type}`
  console.log('actionMap[key]', key)
  return actionMap[key] || ''
}

const contextMap = {
  'post:rental-request:Pending': { receiver: 'owner', sender: 'requester' },
  'patch:rental-request:Canceled': { receiver: 'owner', sender: 'requester' },
  'patch:rental-request:Approved': { receiver: 'requester', sender: 'owner' },
  'patch:rental-request:Rejected': { receiver: 'requester', sender: 'owner' },
  'patch:rental:CanceledByOwner': { receiver: 'rentee', sender: 'owner' },
  'patch:rental:RejectedByCustomer': { receiver: 'owner', sender: 'rentee' },
  'patch:rental:Completed': { receiver: 'rentee', sender: 'owner' },
  'post:inspection:TakeOver': { receiver: 'owner', sender: 'rentee' },
  'post:inspection:HandOver': { receiver: 'owner', sender: 'rentee' },
  'patch:billings:Closed': {
    receiver: 'rental.rentee',
    sender: 'rental.owner'
  },
  'patch:billings:Pending': {
    receiver: 'rental.rentee',
    sender: 'rental.owner'
  },
  'patch:billings:Waived': { receiver: 'rental.rentee', sender: 'rental.owner' }
}

async function transformContextResult(context: any) {
  let method = context.method
  if (method === 'create') {
    method = 'post'
  } else if (method === 'update') {
    method = 'patch'
  }

  const key = `${method}:${context.path}:${
    context.result.status || context.result.type
  }`
  const roles = contextMap[key]

  console.log('contextMap[key]', key)

  if (roles) {
    console.log(
      'context.result[roles.receiver]',
      context.result[roles.receiver]
    )
    console.log('context.result', context.result)
    if (context.path === 'billings') {
      return {
        receiverCpn: context.result.rental.rentee,
        senderCpn: context.result.rental.owner,
        trailerNo: context.result.rental.trailerNumber
      }
    }
    return {
      receiverCpn: context.result[roles.receiver],
      senderCpn: context.result[roles.sender],
      trailerNo: context.result.trailerNumber
    }
  }

  return {
    receiverCpn: 'N/A',
    senderCpn: 'N/A',
    trailerNo: 'N/A'
  }
}
