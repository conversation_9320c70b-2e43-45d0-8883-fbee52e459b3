import {getConfigVar, request} from '../helper/helper';
import * as Response from '../helper/response'
import { Unprocessable } from '@feathersjs/errors'
import { ERROR_MESSAGE } from '../configs/constants'

export default async function isShare(req:any,res:any, next:any) {
  try {
    if (req.user.company){
      let companyId = req.user.company || null;
      let data = {
        method: 'GET',
        url: `${getConfigVar('CTRUrl')}/groups/${companyId}`,
        token: req.headers.authorization
      }
      let result = await request(data);
      if (!result.data.trailerShareStatus) throw new Unprocessable(ERROR_MESSAGE.COMPANY_ACCESS_DENIED);
    }
    next();
  } catch(error: any) {
    Response.error(res, error, error.code)
  }
}
