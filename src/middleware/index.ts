import { Application } from '../declarations';
import configs from '../configs';
import * as response from '../helper/response';
import * as helper from '../helper/helper';
// Don't remove this comment. It's needed to format import lines nicely.
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function

export default function (app: Application) {
  // console.log(app);
}

