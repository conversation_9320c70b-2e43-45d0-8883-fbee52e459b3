import * as helper from '../helper/helper'
import * as errors from '@feathersjs/errors'
import _ from 'lodash'

export default async function auth(context: any) {
  try {
    let data = {
      method: 'GET',
      url: helper.getConfigVar('urlAuth'),
      token: context.params.headers?.authorization ? context.params.headers.authorization : context.params.connection?.token
    }
    let result = await helper.request(data)
    if (result.status !== 200) throw new errors.NotAuthenticated()

    const { user } = result.data
    const role = await context.app.service('roles').Model.findOne({ ctr_id: user.roleAcl }).lean()
    user.etsRoleAcl = _.pick(role, ['modules', 'name', '_id'])
    context.params.user = user

  } catch (e: any) {
    throw new errors.NotAuthenticated(e.message)
  }
}
