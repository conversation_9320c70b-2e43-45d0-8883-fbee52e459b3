import { getConfigVar, prefixLatLng, prefixRoadNameOf, prefixZoneNameOf, request } from '../helper/helper'
import * as helper from '../helper/helper'

import Redis from '../library/redis';


export async function toRecord(payload: { trailer: any, param: any, zoneService: any, isCurrentLocation: boolean }) : Promise<any> {
  const redis = new Redis()
  const {trailer, param, zoneService, isCurrentLocation} = payload;
    try {
      const zoneNameOf = await redis.client.get(`${prefixZoneNameOf}${trailer.trailerNumber.trailerNumber}`)
      if (zoneNameOf && isCurrentLocation) {
        trailer.locationName = zoneNameOf
        return trailer
      }

      const roadNameOf = await redis.client.get(`${prefixRoadNameOf}${trailer.trailerNumber.trailerNumber}`)
      if (roadNameOf && isCurrentLocation) {
        trailer.locationName = roadNameOf
        return trailer
      }
      let coordinates = [];
      if (isCurrentLocation) {
        var locationString = isCurrentLocation && await redis.client.get(`${prefixLatLng}${trailer.trailerNumber.trailerNumber}`);
        if (!locationString) throw new Error('Location not in redis')
        coordinates = JSON.parse(JSON.parse(locationString).location).coordinates
      } else {
        coordinates = JSON.parse(trailer.location).coordinates
      }
      const [lng, lat] = coordinates
      //Get zone name
      let result = await zoneService.findPointInArrCompanies(Number(lat), Number(lng))
      if (Object.keys(result).length && result.zoneName) {
        isCurrentLocation && redis.client.set(`${prefixZoneNameOf}${trailer.trailerNumber.trailerNumber}`,'ZONE_' + result.zoneName, {EX: 60 * 10} )
        trailer.locationName = 'ZONE_' + result.zoneName
        return trailer
      }

      //Road Name
      let roadNameRequest = {
        method: 'POST',
        url: `${helper.getConfigVar('roadUrl')}/road-map/name`,
        token: param?.token || param?.authorization,
        headers: {
          'request-from': 'toRecord'
        },
        body: {
          latitude: `${lat}`,
          longitude: `${lng}`
        }
      }
      let resultRoadName = await helper.request(roadNameRequest)
      const roadName = resultRoadName.data?.name ? 'ROAD_' + resultRoadName.data.name : 'ROAD_NOT_FOUND';
      isCurrentLocation && redis.client.set(`${prefixRoadNameOf}${trailer.trailerNumber.trailerNumber}`,roadName, {EX: 60 * 10} )
      trailer.locationName = roadName
      return trailer
    } catch (err: any) {
      console.log('err', err.message)
      trailer.locationName = 'NO_LOCATION'
      if (err.message?.includes('Location not in redis')) trailer.locationName = 'No Location Provided'
      return trailer
    }
}

export async function cacheLocationTrailerName(payload: { trailer: any, param: any, zoneService: any}) {
  const redis = new Redis()
  const {trailer, param, zoneService} = payload;
  try {
    let coordinates = [];

    if (!trailer.location) throw new Error('Location not in redis')
    coordinates = JSON.parse(trailer.location).coordinates
    const [lng, lat] = coordinates
    //Get zone name
    let result = await zoneService.findPointInArrCompanies(Number(lat), Number(lng))
    if (Object.keys(result).length && result.zoneName) {
      redis.client.set(`${prefixZoneNameOf}${trailer.trailerNumber}`,'ZONE_' + result.zoneName, {EX: 60 * 10} )
      return
    }

    //Road Name
    let roadNameRequest = {
      method: 'POST',
      url: `${helper.getConfigVar('roadUrl')}/road-map/name`,
      token: param?.token || param?.authorization,
      headers: {
        'request-from': 'cacheLocationTrailerName'
      },
      body: {
        latitude: `${lat}`,
        longitude: `${lng}`
      }
    }
    let resultRoadName = await helper.request(roadNameRequest)
    const roadName = resultRoadName.data?.name ? 'ROAD_' + resultRoadName.data.name : 'ROAD_NOT_FOUND';
    redis.client.set(`${prefixRoadNameOf}${trailer.trailerNumber}`,roadName, {EX: 60 * 10} )
  } catch (err: any) {
    console.log('trailer', trailer)
    console.log('err cache location', err.message)
    if (err.message?.includes('Location not in redis'))
    redis.client.set(`${prefixRoadNameOf}${trailer.trailerNumber}`,'No Location Provided', {EX: 60 * 10} )
    return
  }
}
