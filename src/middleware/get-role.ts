import * as helper from '../helper/helper';
import * as errors from '@feathersjs/errors';
export default async function auth(authorization: string) {
  try {
    let data = {
      method: 'GET',
      url: `${helper.getConfigVar('CTRUrl')}/`,
      token: authorization
    }
    let result = await helper.request(data);
    if (result.status !== 200) throw new errors.NotAuthenticated();
    return result.data;
  } catch(e) {
    throw new errors.NotAuthenticated();
  }
}
