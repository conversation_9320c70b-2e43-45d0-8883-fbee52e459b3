import * as errors from '@feathersjs/errors'
import { UserCTR } from '../interface/user'
import { CTVJoinRequest } from '../models/join-request.model'

export async function checkAccessibleDynamicSharing(context: any) {
  const user: UserCTR = context.params.user;
  try {
    if (!user.company) {
      // User is a CDAS admin, so access is granted
      return context
    }

    // Check if user has a join-request with approved status in the service
    const joinRequestService = context.app.service('join-request')
    const joinRequests = await joinRequestService.Model.findOne({
      company: user.company,
      status: 'Approved'
    }).lean()

    if (!joinRequests) {
      throw new errors.Forbidden(
        'Your Organization did not approved for dynamic sharing'
      )
    }
  } catch (error: any) {
    console.log('checkAccessibleDynamicSharing: ', error.message)
    throw error;
  }
  return context
}

export async function isAccessibleDMS(req: any,  res: any, next: any) {
  const user: UserCTR = req.user;
  try {
    if (!user.company) {
      // User is a CDAS admin, so access is granted
      console.error('CDAS admin')
      return next();
    }

    // Check if user has a join-request with approved status in the service
    const joinRequestService = req.app.service('join-request')
    const joinRequests = await joinRequestService.Model.findOne({
      company: user.company,
      status: CTVJoinRequest.status.Approved
    }).lean()

    if (!joinRequests) {
      console.error('Your Organization did not approved for dynamic sharing');
      res.status(403).json({ message: 'Your Organization did not approved for dynamic sharing' })
    }

  } catch (error: any) {
    console.error('checkAccessibleDynamicSharing: ', error.message)
    res.status(error.errorCode).json({message: error.message});
  }
  return next();
}

