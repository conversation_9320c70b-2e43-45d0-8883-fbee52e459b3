// @ts-nocheck

import * as errors from '@feathersjs/errors'
import moment from 'moment'
import { UserCTR, UserRestricted } from '../interface/user'
import { sendErrorToWebhook } from '../helper/helper'
import { HookContext } from '@feathersjs/feathers'
import configs from '../configs'
import { Forbidden } from '@feathersjs/errors'
import { ERROR_MESSAGE } from '../configs/constants'


export default function isCDASAdminOrgAdmin(context: any) {
  try {
    const roleACLS = [
      // UAT
      '6045b8668a1b1c0964d938a7', // Transporter - Admin
      '5fd6d4e61d7a9b2d94f2a833', // CDAS admin
      '61f2191c17944e090e583a3a', // Organization Admin
      // PROD
      '60cb0e8c91c5f2206a5b1550', // CDAS admin
      '60cc00cb64687d093608f57f', // Transporter - Admin
      '60cb0e8d91c5f2206a5b1551' // Transporter - Controller
    ]
    const user = context.params.user
    console.log('user.roleAcl', user.roleAcl)
    if (roleACLS.indexOf(user.roleAcl) < 0) {
      throw new errors.Forbidden('You have no permission for this action')
    }
  } catch (e) {
    throw e
  }
  return context
}

export async function checkAccessible(req: any, res: any, next: any) {
  const user: UserCTR = req.user

  try {
    if (!user.company) {
      // User is a CDAS admin, so access is granted
      return next()
    }

    const licenseService = req.app.service('license')

    // Find an available license for the user's company that is valid today
    const license: any = await licenseService.Model.findOne({
      companyId: user.company,
      startDate: { $lt: moment() },
      endDate: { $gte: moment() },
      isAvailable: true
    }).lean()

    if (!license) {
      // No valid license found, deny access
      throw new errors.Forbidden(
        'Your organization has not subscribed to the eTS service'
      )
    }

    // Check if the user is allowed to use this license
    const userIsAllowed = license.users.some(
      (userRestrict: UserRestricted) => user._id.toString() == userRestrict._id
    )

    if (!userIsAllowed) {
      // User is not allowed to use this license, deny access
      throw new errors.Forbidden(
        'You do not have permission to access the eTS service'
      )
    }
  } catch (error) {
    // Forward any errors to the error handling middleware
    return next(error)
  }

  // If we made it this far, access is granted
  return next()
}

export async function authorizeByRole(
  req: HookContext | Request,
  res?: any,
  next?: any
) {
  function isHookContext(obj: any): obj is HookContext {
    return obj && typeof obj.service === 'object'
  }

  try {
    const { modules } = configs
    let { user } = req.params
    if (!isHookContext(req)) user = req.user

    let path = req.path
    let method = req.method

    let module = modules.find((m) =>
      isHookContext(req) ? m.services.includes(path) : m.apis.includes(path)
    )

    if (user.etsRoleAcl?._id) {
      const userRole = await req.app.service('roles')._get(user.etsRoleAcl._id)
      console.log('userRole: ', userRole.name)
      if (!isMethodAllowed(userRole, method, module?.moduleName)) {
        throw new Forbidden(ERROR_MESSAGE.PERMISSION_DENIED)
      }
    }
  } catch (error) {
    if (next) return next(error)
    throw error
  }

  if (next) return next()
}

function isMethodAllowed(role, method, moduleName) {
  const modulePermissions = role.modules.find(
    (module) => module.moduleName === moduleName
  )

  console.log('modulePermissions', modulePermissions)

  if (!modulePermissions) return true
  method = method.toLowerCase()
  console.log('method', method) 
  switch (method) {
    case 'get':
    case 'find':
      return modulePermissions.allowRead === true

    case 'post':
    case 'create':
      return modulePermissions.allowCreate === true

    case 'patch':
    case 'update':
      return modulePermissions.allowUpdate === true
    
    case 'delete':
    case 'remove':
      return modulePermissions.allowDelete === true

    default:
      throw new Error('Unrecognized or not allowed method')
  }
}



