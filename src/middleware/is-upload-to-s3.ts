import aws from 'aws-sdk'
import multer from 'multer'
import multerS3 from 'multer-s3'
import path from 'path'
import * as helper from '../helper/helper'
import { uploadFileToS3FromStream, uploadMemStorage } from '@src/utils/s3'

export const isFileAllowed = (folder: string | null) => {
  console.log('LOG-folder', folder)
  return (req: any, res: any, next: any) => {
    let s3 = new aws.S3(helper.getConfigVar('awsOptions'))
    return multer({
      storage: multerS3({
        s3: s3,
        bucket: helper.getConfigVar('awsOptions').bucketName,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata: function(req: any, file: any, cb: any) {
          cb(null, { fieldName: file.fieldname })
        },
        key: function(req: any, file: any, cb: any) {
          const filename = file.originalname.replace(/[^a-zA-Z0-9.]/g, '_')
          let key = folder ? `${folder}/${filename}` : `${filename}`
          cb(null, key)
        }
      }),
      fileFilter: function(req, file, callback) {
        const ext = path.extname(file.originalname)
        const mimeType = file.mimetype
        const fileExt = [
          '.png',
          '.jpg',
          '.gif',
          '.jpeg',
          '.xlsx',
          '.pdf',
          '.docx',
          '.doc'
        ]
        const mimeTypes = [
          'image/png',
          'image/jpg',
          'image/jpeg',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/msword'
        ]

        if (!fileExt.includes(ext) && !mimeTypes.includes(mimeType)) {
          return callback(null, false)
        }
        callback(null, true)
      }
    }).array('docs', 5)(req, res, function(err: any) {
      if (err) next(err)
      next()
    })
  }
}

export function uploadFiles({ folder = '', field = 'file', limit = 5, publicAcl = false }) {
  console.log(folder, field, limit)
  return function(req, res, next) {
    return uploadMemStorage.array(field, limit)(req, res, async function(err) {
      if (err) {
        console.log('err', err)
        return next(err)
      }
      try {
        req.files = await Promise.all(
          req.files.map(async (file) => {
            const filename = file.originalname.replace(/[^a-zA-Z0-9.]/g, '_')

            return uploadFileToS3FromStream({
              mimetype: file.mimetype,
              filestream: file.buffer,
              key: folder ? `${folder}/${filename}` : `${filename}`,
              publicAcl: publicAcl
            })
          })
        )
        return next()
      } catch (error) {
        return next(error)
      }
    })
  }
}

export const isUpload = (req: any, res: any, next: any) => {
  let s3 = new aws.S3(helper.getConfigVar('awsOptions'))
  return multer({
    storage: multerS3({
      s3: s3,
      bucket: helper.getConfigVar('aws').bucketName,
      contentType: multerS3.AUTO_CONTENT_TYPE,
      metadata: function(req: any, file: any, cb: any) {
        cb(null, { fieldName: file.fieldname })
      },
      key: function(req: any, file: any, cb: any) {
        let ext = path.extname(file.originalname)
        cb(null, `${Date.now().toString()}${ext}`)
      }
    }),
    fileFilter: function(req, file, callback) {
      const mimeType = file.mimetype
      // if (mimeType !== 'text/csv') {
      //   return callback(null, false);
      // }
      callback(null, true)
    }
  }).array('file', 1)(req, res, function(err: any) {
    next()
  })
}
