import configs from '../configs';
import * as helper from '../helper/helper';
export default async function driverAuth(req: any, res: any, next: any) {
  try {
    let data = {
      method: 'GET',
      url: configs.driverAuth,
      token: req.headers.authorization
    }
    let result = await helper.request(data);
    if (result.status !== 200) return res.status(401).json({message: 'Un Authorize'});
    req.user = result.data;
    next();
  } catch(e) {
    return res.status(500).json({e});
  }
}
