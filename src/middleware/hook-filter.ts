import _ from 'lodash'
import { HookContext } from '@feathersjs/feathers'

export const appendFilterAuditBy = async  (context: HookContext) => {
  const { params } = context
  if (params.query?.createdBy) {
    const resultUsers = await context.app.service('users')._find({
      query: {
        username: {
          $regex: params.query.createdBy,
          $options: 'i'
        },
        $select: ['_id']
      }
    })

    const ids = _.map(resultUsers.data, '_id')
    params.query['createdBy'] = { $in: ids }
  }

  if (params.query?.updatedBy) {
    const resultUsers = await context.app.service('users')._find({
      query: {
        username: {
          $regex: params.query.updatedBy,
          $options: 'i'
        },
        $select: ['_id']
      }
    })

    const ids = _.map(resultUsers.data, '_id')
    params.query['updatedBy'] = { $in: ids }
  }
  return context;
}
