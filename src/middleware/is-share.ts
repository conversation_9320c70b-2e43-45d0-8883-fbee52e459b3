import {getConfigVar, request} from '../helper/helper';
import * as errors from "@feathersjs/errors";
export default async function APIisShare(context: any) {
  try {
    let companyId = context.data || null;
    let data = {
      method: 'GET',
      url: `${getConfigVar('CTRUrl')}/groups/${companyId}`,
      token: context.params.headers.authorization
    }
    let result = await request(data);
    if (!result.data.trailerShareStatus) throw new errors.Forbidden("Your company don't have permission to share trailers");
  } catch(e) {
    throw new errors.Forbidden("Your company don't have permission to share trailers");
  }
}
