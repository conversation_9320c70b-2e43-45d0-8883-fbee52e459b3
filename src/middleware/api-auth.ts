import * as helper from '../helper/helper'
import Redis from '../library/redis'
import { NotAuthenticated } from '@feathersjs/errors'
import * as Response from '../helper/response'
import { ERROR_MESSAGE } from '../configs/constants'
import _ from 'lodash'
import { IRole } from '@src/interface/role'
import { UserETS } from '@src/interface/user'


export default async function authAPI(req: any, res: any, next: any) {
  try {
    const redis = new Redis()
    let data = {
      method: 'GET',
      url: helper.getConfigVar('urlAuth'),
      token: req.headers.authorization
    }
    let userInRedis = await redis.client.get(req.headers.authorization)
    let result = userInRedis ? JSON.parse(userInRedis) : null
    if (!result) {
      result = await helper.request(data)

      if (result.status !== 200)
        throw new NotAuthenticated(ERROR_MESSAGE.UN_AUTHORIZED)
      await redis.client.set(
        req.headers.authorization,
        JSON.stringify({ data: result.data }),
        { EX: 60 }
      )
    }

    
    const { username } = result.data.user
    const user: UserETS = {...result.data.user ,...(await req.app.service('users').Model.findOne({ username}).lean())}
    
    const role: IRole = await req.app
      .service('roles')
      .Model.findOne({ ctr_id: user.roleAcl })
      .lean()  
    user.etsRoleAcl = _.pick(role, ['modules', 'name', '_id'])
    req.user = user
    if (!user.company)  return next()
    const cpnRole: IRole = await req.app
      .service('company-roles')
      .Model.findOne({ _id: user.cpnRole, company: user.company?.toString()})
      .lean() 
      
    if (!cpnRole) return next()

    // merge modules in roleAcl and cpnRole 
    const modules = role.modules.map((module: any) => {
      const cpnModule: any = cpnRole.modules.find(
        (cpnModule: any) => cpnModule.moduleName === module.moduleName
      )
      if (cpnModule) {
        const additional = module.additional?.map((item: any, index) => { // additional [{key: true}]
          const cpnAdditional = cpnModule.additional[index];
          let mergedItem = {};
          for (let key in item) {
            if (cpnAdditional.hasOwnProperty(key)) {
              mergedItem[key] = item[key] && cpnAdditional[key];
            }
          }
          return mergedItem;
        })
        console.log('LOG-additional', additional);
        return {
            ...module,
            allowCreate: cpnModule.allowCreate && module.allowCreate,
            allowRead: cpnModule.allowRead && module.allowRead,
            allowUpdate: cpnModule.allowUpdate && module.allowUpdate,
            allowDelete: cpnModule.allowDelete && module.allowDelete,
            invisible: _.union(cpnModule.invisible, module.invisible),
            additional
        }
      }
      return module
    })
    user.etsRoleAcl.modules = modules
    req.user = user
    return next() 
    // return authorizeByRole(req, res, next)
  } catch (error: any) {
    const errorCode = error.response?.status || error.code
    Response.error(res, error, errorCode)
    // return next(error)
  }
}
