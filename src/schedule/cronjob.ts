import cron from 'cron'
import moment from 'moment'
import * as helper from '../helper/helper'
import Redis from '../library/redis'
import configs from '../configs'
import * as turf from '@turf/turf'
import {
  cronScheduler,
  prefixLatLng,
  sendErrorToWebhook
} from '../helper/helper'
import _ from 'lodash'
import { CTVRental } from '../models/rentals.model'

const CronJob = cron.CronJob

let cronManagement: any = {}

class Queue {
  private tasks: (() => Promise<void>)[] = []
  private isProcessing = false
  private completed = 0
  private failed = 0

  add(task: () => Promise<void>) {
    this.tasks.push(task)
    this.process()
  }

  async process() {
    if (this.isProcessing) return
    this.isProcessing = true

    while (this.tasks.length > 0) {
      const task = this.tasks.shift()
      if (task) {
        try {
          await task()
          this.completed++
        } catch (error) {
          this.failed++
          const status = `Success: ${this.completed}, Failed: ${
            this.failed
          }, Total: ${this.tasks.length + this.completed + this.failed}`
          console.log('status', status)
        }
      }
    }

    this.isProcessing = false
  }

  async counts() {
    return {
      completed: this.completed,
      failed: this.failed,
      total: this.tasks.length + this.completed + this.failed
    }
  }
}

const queue = new Queue()

export default async function (app: any) {
  const redis = new Redis()
  const currentEnv = app.get('env')
  if (currentEnv == 'local') return
  console.log(`Current environment: ${currentEnv}`)

  // Get location
  try {
    const systemSettingService = app.service('system-setting')
    const settings = await systemSettingService._find({
      query: { $sort: { createdAt: -1 }, limit: 1 }
    })

    const runEvery = settings.data?.[0]?.intervalGetLocation || 10
    console.log(`JOB get location will run every: ${runEvery} minutes`)

    const job = cronManagement.jobGetLocation
    if (job) job.stop()

    const jobGetLocation = new CronJob(
      `*/${runEvery} * * * * `,
      async () => {
        try {
          let scheduler = await redis.acquireLock('get_location', 60)
          if (!scheduler) return

          console.log('start cronjob')
          const token = await app.getCDASToken()
          if (!token) throw new Error("Don't find any token")

          const companiesRequest = {
            url: `${app.get('CTRUrl')}/company`,
            method: 'GET',
            token
          }
          // const [{ data: companies }, err] = await wait(helper.request(companiesRequest));
          const { data: companies } = await helper.request(companiesRequest)
          console.log('companies count: ', companies.length)
          const zoneService = app.service('zone-management')
          await zoneService.getArrCompanies(token)

          await Promise.all(
            companies.map(async (company: any) => {
              const trailersRequest = {
                method: 'GET',
                url: `${app.get(
                  'trackingServiceUrl'
                )}/trailer-location?companyId=${company._id}`,
                token
              }
              const { data: trailers } = await helper.request(trailersRequest)

              const isExistRedis = await redis.client.get(
                `${prefixLatLng}${company._id}`
              )
              if (isExistRedis)
                await redis.client.del(`${prefixLatLng}${company._id}`)

              await redis.client.set(
                `${prefixLatLng}${company._id}`,
                JSON.stringify(trailers)
              )

              await Promise.all(
                trailers.map(async (trailer: any) => {
                  trailer.distances = settings.data?.[0]?.distances
                  trailer.token = token
                  trailer.companyId = company._id

                  const task = async () => await syncLocation(trailer)

                  queue.add(task)
                })
              )
            })
          )

          const trailerMonitorService = app.service('trailer-monitor')
          await trailerMonitorService._find({
            query: { limit: 10000, isCache: true },
            token
          }) // 10000 is get all without using cache
          console.log('end cronjob')
        } catch (error: any) {
          console.log(
            `Cronjob get location error: ${
              error.response?.data ?? error.message
            }`
          )
        } finally {
          await redis.releaseLock('get_location')
        }
      },
      null,
      true,
      'UTC'
    )

    const syncLocation = async (processData: any) => {
      const trailer = processData
      if (!trailer.location) return
      const companyId = trailer.companyId
      const coordinates = JSON.parse(trailer.location).coordinates
      const zone = await app
        .service('zone-management')
        .Model.findOne({
          geometry: {
            $geoIntersects: {
              $geometry: {
                type: 'Point',
                coordinates: [Number(coordinates[0]), Number(coordinates[1])]
              }
            }
          },
          companyId: companyId
        })
        .lean()
        .exec()
      const redisLocation = await redis.client.get(
        `${prefixLatLng}${trailer.trailerNumber}`
      )
      let lastLocation: any
      if (redisLocation) lastLocation = JSON.parse(redisLocation)
      if (!redisLocation) {
        console.log('Read location from DB')
        lastLocation = (await app
          .service('trailer-movement-history')
          .Model.findOne({
            trailerNumber: trailer.trailerNumber,
            vehicleNo: trailer.vehicleNo
          })
          .sort({ createdAt: -1 })
          .lean()) as any
      }

      trailer.companyId = companyId
      trailer.isInsideZone = !!zone

      await redis.client.del(`${prefixLatLng}${trailer.trailerNumber}`)
      await redis.client.set(
        `${prefixLatLng}${trailer.trailerNumber}`,
        JSON.stringify(trailer)
      )

      const from = turf.point(JSON.parse(trailer.location).coordinates)
      const to = lastLocation
        ? turf.point(JSON.parse(lastLocation.location).coordinates)
        : null

      let distance = to ? turf.distance(from, to, { units: 'meters' }) : -1
      if (distance >= trailer.distances || !lastLocation) {
        await app.service('trailer-movement-history').Model.create(trailer)
        if (lastLocation.isInsideZone !== trailer.isInsideZone) {
          // get zone name
          let zoneName = 'unset'
          if (trailer.isInsideZone) {
            // @ts-ignore
            zoneName = zone?.zoneName
          } else {
            let coordinates = JSON.parse(lastLocation.location).coordinates
            let zone = await app
              .service('zone-management')
              .Model.findOne({
                geometry: {
                  $geoIntersects: {
                    $geometry: {
                      type: 'Point',
                      coordinates: [
                        Number(coordinates[0]),
                        Number(coordinates[1])
                      ]
                    }
                  }
                },
                companyId: companyId
              })
              .exec()
            // @ts-ignore
            zoneName = zone?.zoneName
          }

          console.log('zoneName', zoneName)

          let action = trailer.isInsideZone
            ? configs.actionNotification.isInsideZone
            : configs.actionNotification.isOutsideZone
          let status =
            action === configs.actionNotification.isInsideZone
              ? 'arrived at'
              : 'departed from'

          const data = {
            trailerNo: trailer.trailerNumber,
            vehicleNo: trailer.vehicleNo,
            companyId: companyId,
            action: action,
            message: `Trailer ${trailer.trailerNumber} is ${status} the zoning area - ${zoneName}`
          }
          await app.service('trailer-notification').Model.create(data)
          app.service('trailer-notification').emit('created', data)

          // Send notification created to company

          const emailSetting = await app
            .service('email-notification-setting')
            ._find({ query: { company: companyId, isEnable: true } })
          console.log('emailSetting: ', emailSetting)
          let receivers = []
          if (emailSetting.total > 0 && emailSetting.data[0].emailReceiver) {
            receivers = emailSetting.data[0].emailReceiver
          }

          const sender =
            app.get('env') == 'production'
              ? '<EMAIL>'
              : '<EMAIL>'
          if (receivers.length > 0) {
            let sendNotifyRequest = {
              url: `${app.get('CTRUrl')}/custom-notification`,
              method: 'POST',
              token: trailer.token.jwt,
              body: {
                from: sender,
                receiver: receivers.join(','),
                type: 'email',
                attachements: 'dGhpcyBpcyB0aGFuZw==',
                filename: 'filename',
                subject: 'Trailer ' + trailer.trailerNumber,
                html: `<p>Trailer ${trailer.trailerNumber} is ${status} the zoning area - ${zoneName}</p>`
              }
            }
            try {
              const result = await helper.request(sendNotifyRequest)
              await app.service('email-storage').create({
                company: companyId,
                requestContent: sendNotifyRequest,
                isSentSuccess: true,
                type: 'Geofencing',
                responseContent: result.data,
                emailReceiver: receivers.join(',')
              })
            } catch (e: any) {
              console.error(
                'Email Geofencing error: ',
                e.response?.data ?? e.message
              )
              await app.service('email-storage').create({
                company: companyId,
                requestContent: sendNotifyRequest,
                emailReceiver: receivers.join(','),
                type: 'Geofencing',
                responseContent: JSON.stringify(e.response?.data) ?? e.message,
                isSentSuccess: false
              })
            }
          }
        }
      }
    }

    jobGetLocation.start()
    cronManagement.jobGetLocation = jobGetLocation
  } catch (error) {
    console.log(`Error at init cronjob getLocation: ${error}`)
  }

  // Email Notification ExpireDate
  try {
    const notificationSetting = app.service('notification-setting')
    const settings = await notificationSetting._find({
      query: {
        isEnable: true,
        $sort: {
          createdAt: -1
        }
      }
    })

    for (const setting of settings.data) {
      const companyId = setting.company

      const job = cronManagement[`checkExpireDate_${companyId}`]

      console.log(`JOB expire of ${companyId} will run: `, setting.scheduler)
      if (job) {
        job.stop()
      }

      // @ts-ignore
      const timeSchedule = cronScheduler[`${setting.scheduler}`]
      console.log('timeSchedule', timeSchedule)
      const checkExpireDate = new CronJob(
        `${timeSchedule}`,
        // '*/15 * * * *',
        async function () {
          try {
            var scheduler = await redis.isScheduler(
              `send_email_` + companyId,
              30
            )
            console.log(`scheduler_send_email_` + companyId, scheduler)
            if (!scheduler) return

            const today = new Date()
            // const today = new Date('2022-11-24T00:00:00.000Z')
            const daysOfRoadTax = new Date(
              new Date().setDate(
                new Date().getDate() + setting.numOfRoadTaxExpiryDate
              )
            )
            const daysOfInsurance = new Date(
              new Date().setDate(
                new Date().getDate() + setting.numOfInsuranceExpiryDate
              )
            )
            const daysOfNextInspection = new Date(
              new Date().setDate(
                new Date().getDate() + setting.numOfNextInspectionExpiryDate
              )
            )
            const daysOfRegistration = new Date(
              new Date().setDate(
                new Date().getDate() + setting.numOfRegistrationExpiryDate
              )
            )

            // let token: any = await app.service('system-ctr').Model.findOne().sort({ createdAt: -1 }).exec()
            const token = await app.getCDASToken()
            if (!token) throw new Error("don't find any token")

            const groupNotification = {
              roadTaxExpiryDate: {} as any,
              insuranceExpiryDate: {} as any,
              nextInspectionDate: {} as any,
              registrationExpiryDate: {} as any
            }
            const trailerService = app.service('trailers')
            const trailersRoadTaxExpiryDate = await trailerService._find({
              token,
              query: {
                limit: 1000,
                'roadTaxExpiryDate[$lte]': daysOfRoadTax.toISOString(),
                'roadTaxExpiryDate[$gte]': today.toISOString(),
                company: companyId
                // 'roadTaxExpiryDate[$lte]': '2022-11-25T00:00:00.000Z',
                // 'roadTaxExpiryDate[$gte]': '2022-11-24T00:00:00.000Z'
              }
            })
            trailersRoadTaxExpiryDate.data.map((trailer: any) => {
              if (
                !groupNotification.roadTaxExpiryDate[`${trailer.company._id}`]
              ) {
                groupNotification.roadTaxExpiryDate[`${trailer.company._id}`] =
                  {
                    email: trailer.company.groupInfo.email,
                    trailers: [trailer]
                  }
              } else
                groupNotification.roadTaxExpiryDate[
                  `${trailer.company._id}`
                ].trailers.push(trailer)
            })

            const trailersInsuranceExpiryDate = await trailerService._find({
              token,
              query: {
                limit: 10000,
                'insuranceExpiryDate[$lte]': daysOfInsurance.toISOString(),
                'insuranceExpiryDate[$gte]': today.toISOString(),
                company: companyId
              }
            })

            trailersInsuranceExpiryDate.data.map((trailer: any) => {
              if (
                !groupNotification.insuranceExpiryDate[`${trailer.company._id}`]
              ) {
                groupNotification.insuranceExpiryDate[
                  `${trailer.company._id}`
                ] = {
                  email: trailer.company.groupInfo.email,
                  trailers: [trailer]
                }
              } else
                groupNotification.insuranceExpiryDate[
                  `${trailer.company._id}`
                ].trailers.push(trailer)
            })

            //--
            const trailersNextInspectionExpiryDate = await trailerService._find(
              {
                token,
                query: {
                  limit: 10000,
                  'nextInspectionDate[$lte]':
                    daysOfNextInspection.toISOString(),
                  'nextInspectionDate[$gte]': today.toISOString(),
                  company: companyId
                }
              }
            )

            trailersNextInspectionExpiryDate.data.map((trailer: any) => {
              if (
                !groupNotification.nextInspectionDate[`${trailer.company._id}`]
              ) {
                groupNotification.nextInspectionDate[`${trailer.company._id}`] =
                  {
                    email: trailer.company.groupInfo.email,
                    trailers: [trailer]
                  }
              } else
                groupNotification.nextInspectionDate[
                  `${trailer.company._id}`
                ].trailers.push(trailer)
            })

            //--
            const trailersRegistrationExpiryDate = await trailerService._find({
              token,
              query: {
                limit: 10000,
                'registrationExpiryDate[$lte]':
                  daysOfRegistration.toISOString(),
                'registrationExpiryDate[$gte]': today.toISOString(),
                company: companyId
              }
            })

            trailersRegistrationExpiryDate.data.map((trailer: any) => {
              if (
                !groupNotification.registrationExpiryDate[
                  `${trailer.company._id}`
                ]
              ) {
                groupNotification.registrationExpiryDate[
                  `${trailer.company._id}`
                ] = {
                  email: trailer.company.groupInfo.email,
                  trailers: [trailer]
                }
              } else
                groupNotification.registrationExpiryDate[
                  `${trailer.company._id}`
                ].trailers.push(trailer)
            })

            console.log('groupNotification', groupNotification)
            for (const key in groupNotification) {
              // @ts-ignore
              const group = groupNotification[key]
              console.log('group', group)
              for (const companyId in group) {
                const trailers = group[companyId].trailers
                // @ts-ignore
                const title = _.upperFirst(key)
                  .match(/[A-Z][a-z]+/g)
                  .join(' ')
                console.log(
                  'Send mail to: ' +
                    companyId +
                    ' title: ' +
                    title +
                    ' Emails: ' +
                    setting.emailReceiver
                )
                const subject = 'CTR - Notification of Trailer (' + title + ')'

                let tableContent = trailers
                  .map(
                    (trailer: any, index: number) => `
  <tr style="background-color: ${index % 2 === 0 ? '#D3D3D3' : '#FFFFFF'};">
    <td style="border: 1px solid black; text-align: center;">${index + 1}</td>
    <td style="border: 1px solid black; text-align: center;">${
      trailer.trailerNumber
    }</td>
    <td style="border: 1px solid black;">${moment(trailer[key]).format(
      'DD/MM/YYYY'
    )}</td>
  </tr>
`
                  )
                  .join('')

                let table = `
  <table style="border-collapse: collapse; border: 1px solid black;">
    <thead>
      <tr>
        <th style="border: 1px solid black;">No</th>
        <th style="border: 1px solid black;">TrailerNumber</th>
        <th style="border: 1px solid black;">${title}</th>
      </tr>
    </thead>
    <tbody>
      ${tableContent}
    </tbody>
  </table>
`

                let emailContent = `
  Dear CTR subscriber,
  <br>
  Please be informed that the below trailers require your attention.
  <br>
  ${table}
  <br>
  Warmest Regards,
  <br>
  CDAS Administrator
  <br>
  <br>
  <strong>** This is an auto-generated email. Please do not reply to this email as your message will not reach your intended recipients. **</strong>
`

                let sendNotifyRequest = {
                  url: `${app.get('CTRUrl')}/custom-notification`,
                  method: 'POST',
                  token,
                  body: {
                    from: '<EMAIL>',
                    receiver: `${setting.emailReceiver.join(',')}`,
                    // 'receiver': `<EMAIL>,<EMAIL>`,
                    type: 'email',
                    attachements: 'dGhpcyBpcyB0aGFuZw==',
                    filename: 'filename',
                    subject: `${subject}`,
                    html: emailContent
                  }
                }
                try {
                  const result = await helper.request(sendNotifyRequest)
                  await app.service('email-storage').create({
                    company: companyId,
                    requestContent: sendNotifyRequest,
                    isSentSuccess: true,
                    type: 'Geofencing',
                    emailReceiver: setting.emailReceiver.join(','),
                    responseContent: result.data
                  })
                } catch (e: any) {
                  console.error(
                    'Email Expiry error: ',
                    e.response?.data ?? e.message
                  )
                  await app.service('email-storage').create({
                    company: companyId,
                    requestContent: sendNotifyRequest,
                    emailReceiver: setting.emailReceiver.join(','),
                    type: 'Geofencing',
                    responseContent: e.response?.data ?? e.message,
                    isSentSuccess: false
                  })
                }
              }
            }
          } catch (error: any) {
            console.log('checkExpireDate Error: ', error.message)
          }
        },
        null,
        true,
        'UTC'
      )

      checkExpireDate.start()
      cronManagement[`checkExpireDate_${companyId}`] = checkExpireDate
    }
  } catch (e) {
    console.log('Cronjob checkExpireDate error: ', e)
  }

  // Toggle enable rental
  try {
    console.log(`JOB update rental will run everyday`)
    const job = cronManagement[`enableRental`]
    if (job) job.stop()

    // @ts-ignore
    const enableRental = new CronJob(
      // `0 0 * * *`,
      '*/1 * * * *',
      async function () {
        try {
          let scheduler = await redis.isScheduler(`enableRental`, 30)
          if (!scheduler) return

          const now = new Date()
          const rentalService = app.service('rental')
          const matchedRental = await rentalService._find({
            query: {
              start: { $lte: now },
              status: CTVRental.status.UpComing,
              $limit: 1000,
              $populate: ['trailerRentalId', 'requestId'],
              $select: ['trailerRentalId', 'requestId', 'status']
            }
          })
          console.log('matchedRental', matchedRental)

          await Promise.all(
            matchedRental.data.map(async (rental: any) => {
              await app.service('rental-request').updateTrailerIsRented({
                rental,
                request: rental.requestId,
                trailerForRent: rental.trailerRentalId,
                params: {}
              })
            })
          )

          await rentalService.Model.updateMany(
            { _id: { $in: _.map(matchedRental.data, '_id') } },
            { status: CTVRental.status.Ready }
          )
        } catch (error: any) {
          console.log('enableRental Error: ', error)
        }
      },
      null,
      true,
      'UTC'
    )

    enableRental.start()
    cronManagement[`enableRental`] = enableRental
  } catch (e) {
    console.log('Cronjob enableRental error: ', e)
  }
}
