// Application hooks that run for every service
// Don't remove this comment. It's needed to format import lines nicely.

import { HookContext } from '@feathersjs/feathers'
import auth from './middleware/auth'
import { checkAccessibleDynamicSharing } from './middleware/permission-in-dynamic-sharing'
import { sendErrorToWebhook } from './helper/helper'

import { iff, isProvider, disallow } from 'feathers-hooks-common'
import { DEFAULT_PAGING } from './configs/constants'
import { authorizeByRole } from './middleware/permission'
import configs from './configs'
import { sendSharingNotification } from './middleware/notification'

// Define a custom hook that enforces the maximum limit of 200
const enforceMaxLimit = () => {
  return (context: any) => {
    // Get the `$limit` query parameter from the request
    const limit = context.params.query.$limit

    // If `$limit` is provided and greater than 200, set it to 200
    if (limit && limit > DEFAULT_PAGING.MAX_LIMIT) {
      context.params.query.$limit = DEFAULT_PAGING.MAX_LIMIT
    }
  }
}

export default {
  before: {
    all: [
      async (context: HookContext) => {
        try {
          await auth(context)
          console.log('-----------Authenticate successfully-----------')
          if (configs.dynamicSharingPaths.includes(context.path)) {
            await checkAccessibleDynamicSharing(context)
            console.log('-----------[checkAccessibleDynamicSharing]-----------')
          }
          return context
        } catch (error) {
          throw error
        }
      },
      iff(
        isProvider('external'),
        // Enforce the maximum limit of 200
        enforceMaxLimit()
      )
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [sendSharingNotification],
    update: [sendSharingNotification],
    patch: [],
    remove: []
  },

  error: {
    all: [
      async (context: HookContext) => {
        const { error } = context
        await sendErrorToWebhook('Error hook: ' + error.message, error)
        return context
      }
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
