import { ObjectId } from 'mongoose'
import { ITrailerRentalAvailability } from './trailer-rental-availability'
import { Id } from '@feathersjs/feathers'

type ExcludedPropertiesExternalAudit = '_id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'deletedBy' | 'createdBy' | 'updatedBy';

interface IAudit {
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date | null;
  createdBy: Id | ObjectId;
  updatedBy: Id | ObjectId;
  deletedBy?: Id | null;
}

interface IMongoPipeline {
  $set?: Partial<ITrailerRentalAvailability>;
  $push?: any;
  $pull?: any;
}

export {
  IAudit,
  ExcludedPropertiesExternalAudit,
  IMongoPipeline
}
