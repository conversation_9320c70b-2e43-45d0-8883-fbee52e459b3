import { Document, ObjectId, Schema } from 'mongoose'
import { ExcludedPropertiesExternalAudit, IAudit } from './app'
import { ITrailerRentalAvailability } from './trailer-rental-availability'
import { Id } from '@feathersjs/feathers'

interface ITrailerRentalSetting extends IAudit  {
  _id: Id;
  isAutoAccept: boolean;
  autoAcceptList: string[];
  whiteList: object;
  company: Id | ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

type ExcludedCreateProperties = '';

interface ICreateTrailerRentalSetting extends Omit<ITrailerRentalSetting, ExcludedPropertiesExternalAudit | ExcludedCreateProperties> {}

export {
  ITrailerRentalSetting,
  ICreateTrailerRentalSetting
}
