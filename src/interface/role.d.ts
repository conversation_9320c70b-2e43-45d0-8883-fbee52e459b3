import { ExcludedPropertiesExternalAudit, IMongoPipeline } from './app'
import { Document, ObjectId, Schema } from 'mongoose'
import { ITrailerRentalAvailability } from './trailer-rental-availability'
import { Id } from '@feathersjs/feathers'
import { CTVRental } from '../models/rentals.model'

interface acl {
  moduleName: string,
  allowCreate: boolean,
  allowRead: boolean,
  allowUpdate: boolean,
  allowDelete: boolean
}
interface IRole extends Document {
  name: string
  ctr_id?: Id
  modules: acl[]
}

type ExcludedCreateProperties = '';

interface ICreateRole extends Omit<IRole, ExcludedPropertiesExternalAudit | ExcludedCreateProperties> {
  users: string[];
}

interface IPatchRole {
}

export {
  IRole,
  ICreateRole,
  IPatchRole
}
