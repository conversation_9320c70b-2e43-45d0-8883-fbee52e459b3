import { CTVBilling } from '../models/billings.model'
import { ExcludedPropertiesExternalAudit, IMongoPipeline } from './app'
import { Document, ObjectId, Schema } from 'mongoose'
import { ITrailerRentalAvailability } from './trailer-rental-availability'
import { Id } from '@feathersjs/feathers'
import { CTVRental } from '../models/rentals.model'

type BillingStatus = keyof typeof CTVBilling.status;
type BillingStatusValue = typeof CTVBilling.status[BillingStatus];

interface IBilling extends Document {
  status: string
  rental: IRental
  inspections: Object[]
  trailerRental: Id | ObjectId
  updatedBy: Id | ObjectId
}

type ExcludedCreateProperties = '';

interface ICreateBilling extends Omit<IBilling, ExcludedPropertiesExternalAudit | ExcludedCreateProperties> {}

interface IPatchBilling {
  status: BillingStatusValue;
}

export {
  IBilling,
  ICreateBilling,
  IPatchBilling
}
