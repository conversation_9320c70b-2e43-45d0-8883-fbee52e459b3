import { ExcludedPropertiesExternalAudit, IAudit, IMongoPipeline } from './app'
import { Id } from '@feathersjs/feathers'
import { IRentingDetails } from './dynamic-sharing'

interface ITrailerInTS {
  _id: Id
  trailerNo: string
  rentingDetails: IRentingDetails
  isEnable: boolean
}
interface ITrailerSharing extends IAudit{
  companyId: string
  trailerIds: Id[]
  trailers: ITrailerInTS[]
  trailerNos: string[]
  isAvailable: boolean
  sharedCompany: Id;
}
type ExcludedCreateProperties = ''

interface ICreateTrailerSharing extends Omit<ITrailerSharing, ExcludedPropertiesExternalAudit | ExcludedCreateProperties> {
  sharedCompanies: Id[]
}

interface IPatchTrailerSharing extends Partial<ITrailerSharing & IMongoPipeline> {}

export {ITrailerSharing, ICreateTrailerSharing}
