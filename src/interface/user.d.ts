import { Document } from 'mongoose'
import { Company } from './etrailer'

export interface UserCTR extends Document {
  status: Number
  allowNotifications: Boolean
  preferredComm: String
  fullname: String
  username: String
  email: String
  mobile: String
  role: String
  roleAcl: String
  company: String
}

export interface UserETS extends UserCTR {
  allowAccess: Boolean,
  cpnRole: String
  etsRoleAcl: any
}

export interface UserRestricted extends UserCTR {
  company: any
}
