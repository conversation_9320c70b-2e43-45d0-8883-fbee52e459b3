import { CTVInspection } from '../models/inspections.model'
import { ExcludedPropertiesExternalAudit, IMongoPipeline } from './app'
import { ObjectId } from 'mongoose'
import { ITrailerRentalAvailability } from './trailer-rental-availability'
import { Id } from '@feathersjs/feathers'

type InspectionType = keyof typeof CTVInspection.type;
type InspectionTypeValue = typeof CTVInspection.type[InspectionType];

interface IInspection {
  type: InspectionTypeValue
  date: Date
  rentee: ICompany
  images: string[]
  owner: ICompany
  trailerNumber: string
  inspector: {
    _id: string
    name: string
    email: string
    company: string
  }
  damageReport: {
    description: string
    photo: string
  }
  rentalId: Id
}

type ExcludedCreateProperties = 'damageReport';

interface ICreateInspection
  extends Omit<
    IInspection,
    ExcludedPropertiesExternalAudit | ExcludedCreateProperties
  > {
  damageReport?: {
    description: string
    photo: string
  }
}

interface IPatchInspection extends Partial<IInspection & IMongoPipeline> {}

export {
  IInspection,
  ICreateInspection,
  IPatchInspection
}
