import { ObjectId, Schema } from 'mongoose'
import { CTVInspection } from '../models/inspections.model'
import { CTVRental } from '../models/rentals.model'
import { ITrailerRentalAvailability } from './trailer-rental-availability'
import { ExcludedPropertiesExternalAudit, IAudit, IMongoPipeline } from './app'
import { IInspection } from './inspection'
import { ICompany, IRentingDetails } from './dynamic-sharing'
import { Id } from '@feathersjs/feathers'
import { IRentalRequest } from './rental-request'

type RentalStatus = keyof typeof CTVRental.status;
type RentalStatusValue = typeof CTVRental.status[RentalStatus];

interface IRental extends IAudit {
  _id: Id | ObjectId;
  trailerRentalId: Id | ObjectId;
  trailerId: ObjectId;
  trailerNumber: string;
  rentee: ICompany;
  owner: ICompany;
  start: Date;
  end: Date;
  status: RentalStatusValue;
  inspections: IInspection[];
  rentingDetails: IRentingDetails;
  estimatedAmount: number;
  numberOfDays: number;
  assignee: any;
  requestId: Id | IRentalRequest;
  changeRequest?: Id | IRentalRequest;
  history?: any[]
}

type ExcludedCreateProperties = 'history' | 'inspections';

interface ICreateRental extends Omit<IRental, ExcludedPropertiesExternalAudit | ExcludedCreateProperties>{
  inspections?: any
}
interface IPatchRental {
  start?: Date;
  end?: Date;
  status?: RentalStatusValue;
  changeRequest?: IRentalRequest;
}

export {
  IRental,
  ICreateRental,
  IPatchRental
}
