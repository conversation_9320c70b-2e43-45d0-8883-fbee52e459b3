import { Document, ObjectId, Schema } from 'mongoose'
import { CTVRentalRequest } from '../models/rental-request.model'
import { ExcludedPropertiesExternalAudit, IAudit, IMongoPipeline } from './app'
import { ICompany, IRentingDetails } from './dynamic-sharing'
import { Id } from '@feathersjs/feathers'

type RentalRequestStatus = keyof typeof CTVRentalRequest.status;
type RentalRequestStatusValue = typeof CTVRentalRequest.status[RentalRequestStatus];

type RentalRequestType = keyof typeof CTVRentalRequest.type;
type RentalRequestTypeValue = typeof CTVRentalRequest.type[RentalRequestType];

interface IRentalRequest extends IAudit {
  _id: Id | ObjectId;
  trailerRentalId: Id | ObjectId;
  rentalId: Id | ObjectId;
  trailerNumber: string;
  requester: ICompany;
  owner: ICompany;
  requestStart: Date;
  requestEnd: Date;
  rejectReason: string;
  message: string;
  estimatedAmount: number;
  numberOfDays: number;
  rentingDetails: IRentingDetails;
  status: RentalRequestStatusValue;
  requestType: RentalRequestTypeValue;
  assignee: Object;
}

type ExcludedCreateProperties = '';

interface ICreateRentalRequest extends Omit<IRentalRequest, ExcludedPropertiesExternalAudit | ExcludedCreateProperties> {}
interface IPatchRentalRequest extends Partial<IRentalRequest & IMongoPipeline> {}
export {
  IRentalRequest,
  ICreateRentalRequest,
  IPatchRentalRequest
}
