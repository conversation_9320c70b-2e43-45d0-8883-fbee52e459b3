export interface Doc {
  _id: string
  updatedAt: string;
  createdAt: string;
 }


export interface TrailerMasterCTR extends Doc {
  trailerNumber: string

  trailerSize: string
  ulwKg: string
  mlwKg: string
  remarks: string;
  createdBy: string;
  trailerStatus: string

  truckNumber: TruckNumber

  roadTaxExpiryDate: string

  insuranceExpiryDate: string
  registrationDate: string

  registrationExpiryDate: string
  nextInspectionDate: string
  color: string
  company: Company
  trailerType: string
  leasee: Lease
}

export interface TrailerMasterETS extends TrailerMasterCTR {
  isSharing: boolean
  isLeasing: boolean
  shareTo: string[]
}

export interface TrailerManagementCTR extends Doc {
  trailerNumber:  TrailerNumber
}

export interface License extends Doc {
  startDate: Date
  endDate: Date
  isAvailable: boolean
  createdBy: string
  companyId: string
  isCreatedByAdmin: boolean
  licenseLimit: Number

  licenseUsed: Number

}
type Company = Doc & {
  name: string
}

type TruckNumber = Doc & {
  vehicleNo: string
}

type TrailerNumber = Doc & {

}
type Lease = Doc & {}
