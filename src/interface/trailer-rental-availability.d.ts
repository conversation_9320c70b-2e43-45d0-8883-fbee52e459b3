import { ObjectId } from 'mongoose'
import { ExcludedPropertiesExternalAudit, IAudit, IMongoPipeline } from './app'
import { Id } from '@feathersjs/feathers'

interface ITrailerRentalAvailability extends IAudit {
  _id: Id;
  trailer: any;
  trailerNumber: string;
  ownerId: Id | ObjectId;
  availableForRentTo: any[];
  rentingDetails: {
    dailyPrice: number;
    monthlyPrice: number;
  };
  isAvailable: boolean;
  isLeasing: boolean;
  rental: Id | ObjectId;
  rentalHistory: Id[];
  origin: ITrailerRentalAvailability
}

type ExcludedCreateProperties = 'rentalHistory';

interface ICreateTrailerRentalAvailability extends Omit<ITrailerRentalAvailability, ExcludedPropertiesExternalAudit | ExcludedCreateProperties> {}
interface IPatchTrailerRentalAvailability extends Partial<ITrailerRentalAvailability & IMongoPipeline> {}

export {
  ICreateTrailerRentalAvailability,
  ITrailerRentalAvailability,
  IPatchTrailerRentalAvailability
}

