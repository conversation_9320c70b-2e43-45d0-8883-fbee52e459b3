import { dataListTrailer, getTrailerById } from './data'
import { String } from 'lodash'

// A type interface for our user (it does not validate any data)
interface TrailerData {
  companyId: String
  zoneName: String
  zoneInstance: String
  createdBy: String
}

export class Trailers {
  async find() {
    return dataListTrailer
  }
  async create(trailer: TrailerData) {
    return {
      ...trailer
    }
  }
  async remove(id: string) {
    console.log('removed', id)
    return id
  }
  async get(id: string) {
    return getTrailerById(id)
  }
  async update(id: string, newData: TrailerData) {
    console.log('update id: ', id)
    /// query data
    return newData
  }
}
