import { format } from 'date-fns'

const skeleton = {
  company: 'aaaaa11111',
  status: 'Allocated',
  size: '20',
  indicator: false,
  currentJob: 'TCLU 1221',
  attachedVehicle: { status: true, id: 'tt232' },
  location: '211',
  remarks1: 'Lorem',
  remarks2: 'Lorem 2',
  lessee: 'transporter',
  licenseStatus: 'N',
  description: 'Trailer from the company',
  trailerMake: 'String',
  trailerModel: 'AXE',
  chassisNo: 'No',
  maximumLadenWeight: 5,
  unladenWeight: 6,
  yearOfManufacturer: format(new Date(), 'dd/MM/yyyy'),
  registrationDate: format(new Date(), 'dd/MM/yyyy'),
  roadTaxExpiryDate: format(new Date(), 'dd/MM/yyyy'),
  insuranceExpiryDate: format(new Date(), 'dd/MM/yyyy'),
  nextInspectionDate: format(new Date(), 'dd/MM/yyyy'),
  registrationExpiryDate: format(new Date(), 'dd/MM/yyyy'),
  colour: 'white',
  truckNo: 'TCLU 1221',
  createdDate: format(new Date(), 'dd/MM/yyyy'),
  createdBy: '61d3d3c8b00d8255a1feaa12',
  editedDate: format(new Date(), 'dd/MM/yyyy'),
  editedBy: '61d3d3c8b00d8255a1feaa12',
  refDate1: format(new Date(), 'dd/MM/yyyy'),
  latLng: {
    latitude: 1.290270,
    longitude: 103.851959
  }
}

const trailerLatLng = {
  id: '61d3d3c8b00d8255a1feaa12',
  latLng: {
    latitude: 1.290270,
    longitude: 103.851959
  },
  lastedUpdate: format(new Date(), 'dd/MM/yyyy')
}

let next = 50;
export const dataListTrailer = [1, 2, 3, 4, 5, 6, 7, 8].map((_, i) => ({
  ...skeleton,
  trailerNo: `TLU-123${_}`,
}))

export const getTrailerById = (id: string) => {
  const filterData = dataListTrailer.filter((item) => item.trailerNo === id)
  return filterData[0]
}

export const dataLatLong = () => {
  let data = [];
  let diff = 30;
  for (let i = 0; i++; i <= 20) {
    if (i > 0 ) trailerLatLng.latLng.latitude += diff;
    data.push(trailerLatLng);
  }
  return data;
}


