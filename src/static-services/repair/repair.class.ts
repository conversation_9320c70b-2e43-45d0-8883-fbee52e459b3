import { repairList, getRepairById } from './repair'
import { String } from 'lodash'

interface RepairData {
  companyId: String
  zoneName: String
  zoneInstance: String
  createdBy: String
}

export class Repairs {
  async find() {
    return repairList
  }
  async create(repair: RepairData) {
    return {
      ...repair
    }
  }
  async get(id: string) {
    return getRepairById(id)
  }
  async update(id: string, newData: RepairData) {
    console.log('update id:', id)
    return newData
  }
}
