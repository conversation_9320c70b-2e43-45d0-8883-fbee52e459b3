const skeleton = {
  damage: 'DR001',
  trailerNo: 'CDAS-SLX23K',
  company: 'Container Depot and Logistics Association',
  status: '-',
  location: 'Company Campus'
}

export const repairList = [1, 2, 3, 4, 5, 6, 7].map((_, i) => ({
  ...skeleton,
  repair: `RR00${_}`
}))

export const getRepairById = (id: string) => {
  const filterData = repairList.filter((item) => item.repair === id)
  return filterData[0]
}
