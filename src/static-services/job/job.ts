
const skeleton = {
  jobId: 'CDAS-ABC001',
  company: 'Container Depot and Logistics Association',
  status: 'In Progress',
  location: 'Company Campus',
  currentTask: 'TCLU 19338000'
}

export const jobList = [1, 2, 3, 4, 5, 6, 7].map((_, i) => ({
  ...skeleton,
  jobId: `CDAS-ABC00${_}`
}))
 export const getJobById = (id:string) => {
  const filterData = jobList.filter((item) => item.jobId === id)
  return filterData[0]
 }
