import { jobList, getJobById } from './job'
import { String } from 'lodash'

interface JobData {
  companyId: String
  zoneName: String
  zoneInstance: String
  createdBy: String
}

export class Jobs {
  async find() {
    return jobList
  }
  async create(job: JobData) {
    return { ...job }
  }
  async get(id: string) {
    return getJobById(id)
  }
  async update(id: string, newData: JobData) {
    console.log('update job id:', id)
    return newData
  }
}
