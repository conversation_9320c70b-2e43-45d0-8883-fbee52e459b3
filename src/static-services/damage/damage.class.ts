import { damageList, getDamageById } from './damage'
import { String } from 'lodash'

interface DamageData {
  companyId: String
  zoneName: String
  zoneInstance: String
  createdBy: String
}

export class Damages {
  async find() {
    return damageList
  }
  async create(damage: DamageData) {
    return {
      ...damage
    }
  }
  async get(id: string) {
    return getDamageById(id)
  }
  async remove(id: string) {
    return id
  }
  async update(id: string, newData: DamageData) {
    console.log('update id:', id)
    return newData
  }
}
