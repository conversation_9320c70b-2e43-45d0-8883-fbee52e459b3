const skeleton = {
  damage: 'DR001',
  company: 'Container Depot and Logistics Association',
  status: 'Reported',
  location: 'Company Campus',
  latestReport: 'task EC123'
}

export const damageList = [1, 2, 3, 4, 5, 6, 7].map((_, i) => ({
  ...skeleton,
  trailerNo: `TLU-123${_}`
}))

export const getDamageById = (id: string) => {
  const filterData = damageList.filter((item) => item.trailerNo === id)
  return filterData[0]
}
