import mongoose from 'mongoose';
import { Application } from './declarations';
import logger from './logger';
import os from 'os';


export default function (app: Application): void {
  console.log('Mongose URL ', app.get('mongodb'));
  if (app.get('env') !== 'production') {
    // mongoose.set('debug', true)
    // mongoose.set('debug', { color: true })
  }

  mongoose.connect(
    app.get('mongodb')
  ).catch(err => {
    logger.error(err);
    process.exit(1);
  });


  // setInterval(() => {
  //   console.log('Mongoose connections length: ', mongoose.connections.length)
  //   console.log('Number of cores: ', os.cpus().length)
  //   console.log('Memory in use: ', process.memoryUsage().rss / 1024 / 1024)
  // }, 5000)
  app.set('mongooseClient', mongoose);

}
