
const error = (res:any, err:any, code?:any) => {
  let msgErr = err;
  if (typeof err === 'object' && typeof err.message !== 'undefined') {
    msgErr = err.message;
  }
  res.statusCode = code ? code : err.code ?  err.code : 500;
  return res.json({ message: msgErr });
};

const success = (res:any, data:any, code?:any) => {
  res.statusCode = code || 200;
  return res.json(data);
};

export {
  error,
  success
};
