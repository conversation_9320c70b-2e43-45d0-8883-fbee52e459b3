import { to } from 'await-to-js'
import axios from 'axios'

const feathers = require('@feathersjs/feathers')
const configuration = require('@feathersjs/configuration')
// Use the application root and `config/` as the configuration folder
import _ from 'lodash'
import Redis from '../library/redis'
import { randomString } from '../utils/string'

const app = feathers().configure(configuration())

const prefixLatLng = 'location-'
const CTR_REQUEST = 'CTR-Request-'
const CTR_RESPONSE = 'CTR-Response-'
const prefixZoneNameOf = 'zoneNameOf-'
const prefixRoadNameOf = 'roadNameOf-'
const prefixZone = 'zone-'
const prefixZoneAdmin = 'zone-Admin-'
const cronScheduler = {
  // 'Hourly': '0 * * * *',
  Daily: '0 1 * * *',
  Weekly: '0 1 * * 1',
  Monthly: '0 1 1 * *', //
  Fortnightly: '0 1 */15 * *' //every half month (15 - 30)
}

const request = async (data: any) => {
  /* ... */
  let options = {
    method: data.method,
    url: data.url,
    headers: {
      Authorization: data.token || null,
      'content-type': 'application/json',
      'cache-control': 'no-cache',
      ...data.headers
    },
    timeout: 30000,
    data: data.body
  }
  return await axios(options)
}

const wait = async (_promise: any) => {
  let err: any
  let res: any
  ;[err, res] = await to(_promise)
  if (err) return [err]
  return [null, res]
}

const getFilterGetAll = (_param: any) => {
  let skip = (_param.query.page - 1) * _param.query.rowsPerPage
  let rowsPerPage =
    _param.query.rowsPerPage > 0
      ? _param.query.rowsPerPage
      : _param.query.rowsNumber
  if (skip < 0) skip = 0
  let filter: string = ''
  let ObjFilter = _param.query.filter
  for (const key in ObjFilter) {
    if (ObjFilter[key]) filter += `${key}=${ObjFilter[key]}`
  }
  return { skip, rowsPerPage, filter }
}

const getQueryConditionHistoryTrailer = (param: any) => {
  let skip = (param.query.page - 1) * param.query.rowsPerPage
  let rowsPerPage =
    param.query.rowsPerPage > 0
      ? param.query.rowsPerPage
      : param.query.rowsNumber
  if (skip < 0) skip = 0
  let ObjFilter = param.query
  //if (!ObjFilter.trailerId) throw Error("TrailerId is required");
  let query = {
    ...ObjFilter,
    $limit: 10000,
    $sort: {
      createdAt: -1
    }
  }
  if (skip >= 0) query.$skip = skip
  if (rowsPerPage) query.$limit = rowsPerPage
  return query
}
const getConfigVar = (name: string) => {
  return app.get(name)
}
const getKeyByValue = (object: any, value: any) => {
  return Object.keys(object).find((key) => object[key] === value)
}

const sendErrorToWebhook = async (message: string, data: any) => {
  console.error('sendErrorToWebhook: ' + randomString(4), data?.message ?? data)
  // let alert = {
  //   url: `https://work.ftech.ltd/hooks/emh14u475t8ofj5x9a6o873kio`,
  //   method: 'POST',
  //   body: {
  //     text: `${app.get('env')} ` + randomString(4) + ' ' + message + '\n```\n' + JSON.stringify(data) + '\n```\n'
  //   }
  // }
  // await request(alert)
}

function appHelper(app: any): void {
  const redis = new Redis()
  app.getCDASToken = async () => {
    const tokenInRedis = await redis.client.get('ets-cdasToken')
    if (tokenInRedis) return tokenInRedis

    const url = `${app.get('CTRUrl')}/authentication`
    const response = await request({
      method: 'POST',
      url,
      body: {
        strategy: 'local',
        email: app.get('cdasAccount').username,
        password: app.get('cdasAccount').password,
        device: {
          os: 'computer',
          FCMId: 'portal'
        },
        remember: true
      }
    })

    redis.client.set('ets-cdasToken', response.data.accessToken, {
      EX: app.get('tokenLifetime')
    })
    return response.data.accessToken
  }
  app.sendCustomNotification = async ({
    type,
    subject,
    receiver,
    companyId,
    typeStorage,
    content
  }: any) => {
    const sender =
      app.get('env') == 'production'
        ? '<EMAIL>'
        : '<EMAIL>'
    let envReceiver = receiver
    if (type == 'email') {
      // envReceiver =
      //   app.get('env') == 'production' ? receiver : '<EMAIL>'
    }
    let sendNotifyRequest = {
      url: `${app.get('CTRUrl')}/custom-notification`,
      method: 'POST',
      token: await app.getCDASToken(),
      body: {
        from: sender,
        receiver: envReceiver,
        type,
        attachements: 'dGhpcyBpcyB0aGFuZw==',
        filename: 'filename',
        subject,
        html: `<p>${content}</p>`,
        description: `${content}`
      }
    }

    try {
      const result = await request(sendNotifyRequest)
      // const result = { data: '' }
      console.log('companyId', companyId)
      await app.service('email-storage')._create({
        company: companyId,
        requestContent: sendNotifyRequest,
        isSentSuccess: true,
        type,
        responseContent: result.data,
        emailReceiver: receiver
      })
      return result
    } catch (e: any) {
      console.error('Send notification error: ', e.response?.data ?? e.message)
      await sendErrorToWebhook('Send notification error: ', {
        sendNotifyRequest,
        error: e.response?.data ?? e.message
      })
      await app.service('email-storage')._create({
        company: companyId,
        requestContent: sendNotifyRequest,
        emailReceiver: receiver,
        type: typeStorage,
        responseContent: JSON.stringify(e.response?.data) ?? e.message,
        isSentSuccess: false
      })
    }
  }
}

async function onStartUp(app: any): Promise<void> {
  const redis = new Redis()

  let scheduler = await redis.isScheduler(`onStartUp`, 30)
  console.log('scheduler', scheduler)
  if (!scheduler) return

  // assign company to trailer allocation
  const trailerAllocate = await app
    .service('trailer-allocation')
    .Model.find({ company: { $exists: false } })
    .lean()
    .exec()
  const trailerIds = _.map(trailerAllocate, 'trailerId')
  const url =
    `${app.get('CTRUrl')}/trailer-master?$limit=10000` +
    trailerIds
      .map((element, index) => {
        return `&_id[$in][]=${element}`
      })
      .join('')

  if (!trailerIds.length) return
  try {
    let data = {
      method: 'GET',
      url,
      token: await app.getCDASToken()
      // token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE2OTI4NTc1MTEsImlhdCI6MTY5Mjg1NzUxMCwiZXhwIjoxNjkzMDMwMzEwLCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MTE5Y2M3NTJjMjBlNzA5MTkyM2M2MmIiLCJqdGkiOiIwNDg0Yjg0Ni1hMzZlLTRlMTktYTYzNy0yZWJmYTFlMWU0ZjcifQ.IX6FUEis6U6V94awY_mjTFWuklsmTc9gOEVvds0WxuI'
    }

    let { data: trailerMaster } = await request(data)
    const map2 = _.groupBy(trailerMaster.data, 'company._id')
    await Promise.all(
      Object.keys(map2).map(async (key) => {
        await app
          .service('trailer-allocation')
          .Model.updateMany(
            { trailerId: { $in: _.map(map2[key], '_id') } },
            { company: key }
          )
      })
    )
  } catch (e: any) {
    console.log('Error onStartUp function', e.message)
  }
  // console.log('trailerAllocate', trailerAllocate)
}

export {
  appHelper,
  cronScheduler,
  CTR_REQUEST,
  CTR_RESPONSE,
  getConfigVar,
  getFilterGetAll,
  getKeyByValue,
  getQueryConditionHistoryTrailer,
  onStartUp,
  prefixLatLng,
  prefixRoadNameOf,
  prefixZone,
  prefixZoneAdmin,
  prefixZoneNameOf,
  request,
  sendErrorToWebhook,
  wait
}
