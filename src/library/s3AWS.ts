import { getSignedUrlForS3, uploadFileToS3FromStream } from '@src/utils/s3'
import mime from 'mime-types'

export default class S3AWS {

  constructor() {
  }

  public async upload(localData: any, fileName: string) {
    return uploadFileToS3FromStream({
      mimetype:  mime.lookup(fileName) || 'application/octet-stream',
      filestream: localData,
      key: fileName,
      publicAcl: false,
    })
      .then(res => {
        return this.getUrl(fileName)
      })
      .catch(err => {
        console.log('Upload failed:', err)
        return err
      })
  }


  public async getUrl(key: string, expireTime: number = 3600): Promise<string | boolean> {
    console.log('LOG-key', key);
    if (key.includes('http')) {
      key = key.split('.com/').pop()
    }
    console.log('LOG-key', key);
    return getSignedUrlForS3(key, expireTime)
  }

}



