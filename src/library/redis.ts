import * as redis from "redis";

let connectUrl;
let count = 0;
let client;


export default class Redis {
  client
  constructor() {
    if (client) {
      this.client = client
      return this
    }
    count++
    client = this.client = redis.createClient({
      url: connectUrl
    })
    this.client.on('success', function () {
      console.log(`Connect Success To Redis`)
    })

    this.client.on('error', function (err) {
      console.log('redis', err)
    })
    this.client
      .connect()
      .then()
      .catch((e) => {
        console.log('Redis error: ', e)
      })
  }

  async isScheduler(type: string, expire = 0) {
    const key = 'scheduler-' + type
    const check = await this.client.incr(key)
    if (check > 1) return null
    await this.client.expire(key, expire || 60)
    return key
  }

  async acquireLock(lockKey: string, expireTime?: number): Promise<boolean> {
    const result = await this.client.set(lockKey, 'locked', {
      EX: expireTime || 60,
      NX: true
    })
    return result === 'OK'
  }

  async releaseLock(lockKey: string): Promise<boolean> {
    const result = await this.client.del(lockKey)
    return result === 1
  }

  async rateLimit(params: { key: string; limit: number; expire: number }) {
    const { key, limit, expire } = params
    const redisKey = 'RATE_LIMIT:' + key
    const value = await this.client.incr(redisKey)
    if (value > limit) {
      return false
    }
    if (value === 1) {
      await this.client.expire(key, expire)
    }
    return true
  }
}

export function setConnectUrl(url) {
  connectUrl = url;
  console.log('LOG-setConnectUrl', connectUrl)
}
