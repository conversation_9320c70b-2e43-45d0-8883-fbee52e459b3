'use strict'
import dotenv from 'dotenv'

dotenv.config()

export default {
  driverAuth: process.env.DRIVER_AUTH_URL,
  urlAuth: process.env.AUTH_URL,
  ctrUrl: process.env.CTR_URL,
  tmsUrl: process.env.TMS_URL,
  awsSecretKey: process.env.AWS_SECRET_KEY,
  awsAccessKey: process.env.AWS_ACCESS_KEY,
  awsBucketName: process.env.AWS_BUCKET_NAME,
  statusDamageReport: {
    draft: 0,
    new: 1,
    approved: 2,
    closed: 3,
    canceled: 4,
    void: 5
  },
  statusZone: {
    active: 0,
    inactive: 1
  },
  actionNotification: {
    isInsideZone: 0,
    isOutsideZone: 1
  },
  dataListTrailer: [
    {
      trailerNo: 'TLU-4567',
      company: {
        id: 'aaaaa11111',
        name: 'beeknights'
      },
      status: 1,
      size: '20',
      indicator: false,
      currentJob: 'TCLU 1221',
      attachedVehicle: '',
      remarks1: 'Lorem',
      remarks2: 'Lorem 2',
      refDate1: new Date()
    },
    {
      trailerNo: 'TLU-4567',
      company: {
        id: 'aaaaa11111',
        name: 'beeknights'
      },
      status: 1,
      size: '20',
      indicator: false,
      currentJob: 'TCLU 1221',
      attachedVehicle: '',
      remarks1: 'Lorem',
      remarks2: 'Lorem 2',
      refDate1: new Date()
    }
  ],
  dataDetailTrailer: {
    trailerNo: 'TLU-4567',
    company: {
      id: 'aaaaa11111',
      name: 'beeknights'
    },
    status: 1
  },
  dataJob: {
    id: '61d3d3c8b00d8255a1feaa12',
    status: 1,
    attachedTrailer: 'TLU-4567'
  },
  modules: [
    {
      moduleName: 'Master List',
      services: ['trailers'],
      apis: [],
      invisible: []
    },
    {
      moduleName: 'Monitoring',
      services: [
        'trailer-monitor',
        'trailer-monitor-config',
        'trailer-monitor-history'
      ],
      apis: [],
      invisible: []
    },
    {
      moduleName: 'Tracking',
      services: ['trailer-movement-history'],
      apis: ['/trailers-overview'],
      invisible: ['allowCreate', 'allowUpdate', 'allowDelete']
    },
    {
      moduleName: 'Geofencing',
      services: ['zone-management'],
      apis: [
        '/geofencing/list',
        '/geofencing/export-xls',
        '/zone-management/lat-lng'
      ],
      invisible: []
    },
    {
      moduleName: 'Damages',
      services: ['damage-report'],
      apis: ['/damage-report-export-xls'],
      invisible: []
    },
    {
      moduleName: 'Repairs',
      services: ['repair-report'],
      apis: ['/repair-report-export-xls'],
      invisible: []
    },
    // { moduleName: 'System Settings', services: ["system-setting"], apis: [] },
    {
      moduleName: 'Roles',
      services: ['roles'],
      apis: [],
      invisible: ['allowCreate']
    },
    {
      moduleName: 'Licensing',
      services: ['license'],
      apis: ['/license/add-members'],
      invisible: []
    },
    {
      moduleName: 'Notification',
      services: ['trailer-notification'],
      apis: [],
      invisible: ['allowCreate', 'allowDelete']
    },
    // terms and conditions
    {
      moduleName: 'Sharing Terms Conditions',
      services: ['sharing-terms-conditions'],
      apis: ['/sharing-terms-conditions/upload'],
      invisible: ['allowCreate', 'allowRead', 'allowDelete']
    },
    // listing
    {
      moduleName: 'Lease IN | Listing',
      services: ['trailer-rental-availability'],
      apis: ['/trailer-rental-availability/view'],
      invisible: ['allowCreate', 'allowUpdate', 'allowDelete']
    },
    {
      moduleName: 'Lease OUT | Listing',
      services: ['trailer-rental-availability'],
      apis: [
        '/trailer-rental-availability/multiple',
        '/trailer-rental-availability/management'
      ],
      invisible: []
    },
    // request
    {
      moduleName: 'Lease IN | Request',
      services: ['rental-request'],
      apis: ['/rental-request/outgoing'],
      invisible: ['allowUpdate', 'allowDelete'],
      additional: ['allowCancel']
    },
    {
      moduleName: 'Lease OUT | Request',
      services: ['rental-request'],
      apis: ['/rental-request/incoming'],
      invisible: ['allowCreate', 'allowUpdate', 'allowDelete'],
      additional: ['allowReject', 'allowApprove']
    },
    // live lease
    {
      moduleName: 'Lease IN | Live Lease',
      services: ['rental'],
      apis: ['rental/outgoing'],
      invisible: ['allowCreate', 'allowUpdate', 'allowDelete'],
      additional: ['allowStart', 'allowComplete', 'allowReject']
    },
    {
      moduleName: 'Lease OUT | Live Lease',
      services: ['rental'],
      apis: ['/rental/incoming'],
      invisible: ['allowCreate', 'allowUpdate', 'allowDelete'],
      additional: ['allowConfirm', 'allowCancel']
    },
    // billing
    {
      moduleName: 'Lease IN | Billing',
      services: ['billings'],
      apis: ['/billings/lease-in'],
      invisible: ['allowUpdate', 'allowCreate']
    },
    {
      moduleName: 'Lease OUT | Billing',
      services: ['billings'],
      apis: ['/billings/lease-out'],
      invisible: ['allowCreate']
    },
    {
      moduleName: 'Administration | Company Roles',
      services: ['company-roles'],
      apis: [],
      invisible: []
    }
  ],
  dynamicSharingPaths: [
    'trailer-rental-availability',
    'rental-request',
    'rental',
    'inspection'
  ]
}
