import moment from 'moment-timezone'
import mongoose from 'mongoose'
import * as uuid from 'uuid'
import * as _ from 'lodash'
// import words from './censors.json'

// const censors = words.map((x: string) => new RegExp(x.replace(/\*/g, '\\*'), 'i'))

declare global {
  interface String {
    singleChar: (char?: string) => string;
    toItemUid: () => string;
    capitalize: () => string;
  }
}

String.prototype.singleChar = function(char: string = ' '): string {
  return this.replace(new RegExp(`${char}+`), char).trim()
}

String.prototype.toItemUid = function(): string {
  return this.replace(/\s/g, '_').toLowerCase()
}

String.prototype.capitalize = function(): string {
  return this.charAt(0).toUpperCase() + this.slice(1).toLowerCase()
}

function formatDateWithTimezone(date: Date | string, tz: string = 'Asia/Ho_Chi_Minh', format: string = 'LLL'): string {
  return moment(date || '')
    .tz(tz)
    .format(format)
}

function generateQrcode(size: number = 12): string {
  return generateCode(size, false).toLowerCase()
}

function generateCode(n: number = 6, numberOnly: boolean = true): string {
  let text = ''
  let charset = ''
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const numOnly = '0123456789'
  if (numberOnly) {
    charset = numOnly
  } else {
    charset = possible
  }
  for (let i = 0; i < n; i++) {
    text += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  return text
}

function randomString(
  length: number = 8,
  { number = true, character = true, special = false } = {}
): string {
  const CHARACTERS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  const SPECIALS = '!@#$%^&*.+-/|~'
  const NUMBERS = '1234567890'
  const charset = [number && NUMBERS, character && CHARACTERS, special && SPECIALS]
    .filter(s => s)
    .join('')
    .split('')
  return Array(length)
    .fill(0)
    .map(() => _.sample(charset))
    .join('')
}

function generateRedisKey(type: string, user_id: string): string {
  return `${process.env.APP_NAME || 'SUPER_BINANCE'}_${type}_${user_id}`
}

function generateSingleKey(type: string): string {
  return `${process.env.APP_NAME || 'SUPER_BINANCE'}_${type}`
}

function getRedisKey(...str: string[]): string {
  return `${process.env.APP_NAME || 'SUPER_BINANCE'}_${str.join('_')}`
}

function formatDate(date: Date | string, format: string = 'LLL', tz: string = 'Asia/Ho_Chi_Minh'): string {
  return moment(date).tz(tz).format(format)
}

function isObjectId(id: string): boolean {
  return mongoose.Types.ObjectId.isValid(id)
}

// function isValidName(name: string): boolean {
//   return !censors.find(x => x.test(name))
// }

function generateUUID(): string {
  return uuid.v4()
}

function getWeek(date: Date): string {
  const year = moment(date).tz('Asia/Ho_Chi_Minh').startOf('week').get('years')
  const week = moment(date).tz('Asia/Ho_Chi_Minh').get('isoWeeks')
  return `${year}_${(week + '').padStart(2, '0')}`
}

function mapDataToString(pattern: string, data: Record<string, any>): string {
  return pattern.replace(/\{\{(\w+)\}\}/g, (matched, captured) => (data[captured] !== undefined ? data[captured] : matched))
}

function maskMobileNumber(mobileNumber: string): string {
  return '**** ' + mobileNumber.slice(-4)
}

function normalizeNotionString(
  str: string,
  // cascade
  charMap: [string | RegExp, string][] = [
    [/\n/, ''],
    [/\s+/, ' ']
  ]
): string {
  if (!str) return ''
  let result = str
  for (const [pattern, replacement] of charMap) result = result.replace(new RegExp(pattern, 'g'), replacement).trim()
  return result
}

export {
  generateCode,
  formatDateWithTimezone,
  generateRedisKey,
  formatDate,
  isObjectId,
  generateSingleKey,
  // isValidName,
  generateUUID,
  getWeek,
  generateQrcode,
  randomString,
  getRedisKey,
  mapDataToString,
  maskMobileNumber,
  normalizeNotionString
}
