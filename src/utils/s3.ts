import fs from 'fs'
import { generateUUID } from './string'
import mime from 'mime-types'

import {
  CopyObjectCommand, DeleteObjectCommand, GetObjectCommand,
  HeadObjectCommand,
  ListObjectsCommand,
  PutObjectCommand,
  S3Client
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

import { getConfigVar } from '@src/helper/helper'
import multer from 'multer'

let s3Client: S3Client

export const uploadMemStorage = multer({
  storage: multer.memoryStorage()
})

export function getS3Client(force = false) {
  if (!s3Client || force) {
    console.log('config', getConfigVar('awsOptions'))
    s3Client = new S3Client({
      region: getConfigVar('awsOptions').region,
      credentials: getConfigVar('awsOptions')
    })
  }
  return s3Client
}

export function getImageUrl(fileByPath: string) {
  return `https://${getConfigVar('awsOptions').bucketName}.s3-${getConfigVar(
    'awsOptions').region}.amazonaws.com/${fileByPath}`
}

export async function getSignedUrlForS3(key: string, expireTime: number = 3600) {
  const params = {
    Bucket: getConfigVar('aws').bucketName,
    Key: key,
    Expires: expireTime
  }
  return getSignedUrl(getS3Client() as any, new GetObjectCommand(params), { expiresIn: expireTime })
}

export function moveFile(oldPath: string, newPath: string) {
  const params = new CopyObjectCommand({
    Bucket: getConfigVar('awsOptions').bucketName,
    CopySource: `${getConfigVar('awsOptions').bucketName}/${oldPath}`,
    Key: newPath,
    ACL: 'public-read'
  })
  return getS3Client().send(params)
}

export async function listFilesInFolder(folderPath: string) {
  const params = new ListObjectsCommand({
    Bucket: getConfigVar('awsOptions').bucketName,
    Prefix: folderPath
  })

  try {
    const response = await getS3Client().send(params)
    const files = response.Contents
    console.log('files', files)

    // Extract the file names from the response
    return files?.filter((f) => f.Size).map((file) => file.Key) || []
  } catch (error) {
    console.error('Error:', error)
    return []
  }
}

export function removeFile(filepath: string) {
  const params = new DeleteObjectCommand({
    Bucket: getConfigVar('awsOptions').bucketName,
    Key: filepath
  })
  return getS3Client().send(params)
}

export function checkIfFileExists(filePath: string) {
  const params = new HeadObjectCommand({
    Bucket: getConfigVar('awsOptions').bucketName,
    Key: filePath
  })
  return getS3Client().send(params)
}

export function uploadXLSX({ key, filePath }) {
  return getS3Client().send(
    new PutObjectCommand({
      Bucket: getConfigVar('awsOptions').bucketName,
      Key: key,
      Body: fs.createReadStream(filePath),
      ContentType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      // ACL: 'public-read',
    })
  )
}

export async function uploadFileToS3FromStream({
  mimetype = 'image/png',
  filestream,
  key = generateUUID(),
  publicAcl = true
}): Promise<any> {
  const s3Client = getS3Client()

  await s3Client.send(
    new PutObjectCommand({
      Bucket: getConfigVar('awsOptions').bucketName,
      Key: key,
      Body: filestream,
      ContentType: mimetype,
      ACL: publicAcl ? 'public-read' : 'private'
    })
  )

  return {
    key,
    url: getImageUrl(key)
  }
}

export async function uploadFileToS3(
  filepath: string,
  key: string = '',
  publicAcl: boolean = true
): Promise<string> {
  const s3Client = getS3Client()

  await s3Client.send(
    new PutObjectCommand({
      Bucket: getConfigVar('awsOptions').bucketName,
      Key: key,
      Body: fs.createReadStream(filepath),
      ContentType: mime.lookup(filepath) || 'application/octet-stream',
      ACL: publicAcl ? 'public-read' : 'private'
    })
  )

  return getImageUrl(key)
}

export async function deleteS3Folder(folder: string) {
  try {
    const s3Client = getS3Client()
    const Bucket = getConfigVar('awsOptions').bucketName
    const result = await s3Client.send(
      new ListObjectsCommand({ Bucket, Prefix: folder })
    )
    return await Promise.all(
      result.Contents.map((content) =>
        s3Client.send(new DeleteObjectCommand({ Bucket, Key: content.Key }))
      )
    )
  } catch (error) {
    console.error(error)
    return false
  }
}

export async function downloadFileFromS3(key: string, downloadPath: string) {
  const command = new GetObjectCommand({
    Bucket: getConfigVar('awsOptions').bucketName,
    Key: key
  })

  try {
    const response = await getS3Client().send(command)

    // Create a writable stream to save the file
    const fileStream = fs.createWriteStream(downloadPath)

    // Pipe the response stream to the file stream
    // @ts-ignore
    response.Body.pipe(fileStream)

    // Wait for the file to finish writing
    await new Promise((resolve, reject) => {
      fileStream.on('finish', resolve)
      fileStream.on('error', reject)
    })

    console.log('File downloaded successfully!')
    return true
  } catch (error) {
    console.error('Error:', error)
    return false
  }
}
