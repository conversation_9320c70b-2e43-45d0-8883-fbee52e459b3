import * as validate from 'validate-typescript';
export default {
  create: {
    companyId: validate.Type(String),
    zoneName: validate.Type(String),
    zoneInstance: validate.Type(String)
  },
  update: {
    id: validate.Type(String),
    companyId: validate.Type(String),
    zoneName: validate.Type(String),
    zoneInstance: validate.Type(String)
  },
  latLng : {
    latitude: validate.Type(String),
    longitude: validate.Type(String)
  }
}

