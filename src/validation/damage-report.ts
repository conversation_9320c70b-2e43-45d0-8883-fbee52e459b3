import * as validate from "validate-typescript";

export default {
  create: {
    trailerId: validate.Optional(validate.Type(String)),
    trailerNo: validate.Optional(validate.Type(String)),
    companyId: validate.Optional(validate.Type(String)),
    description: validate.Optional(validate.Type(String)),
    submittedDate: validate.Optional(validate.Type(String)),
    reportId: validate.Optional(validate.Type(String))
  }
}
