// damage-report-audit-logs-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'damageReportAuditLogs';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({
    damageReportId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    trailerId: {
      type: String,
      required: true
    },
    trailerNo: {
      type: String,
      required: true
    },
    companyId: {
      type: String,
      required: true
    },
    reportId: {
      type: String,
      required: true
    },
    description: {
      type: String,
      max: 1000
    },
    status: {
      type: Number,
      required: true,
      default: 0
    },
    reportDate: {
      type: Date,
      default: Date.now
    },
    submittedBy: {
      type: Schema.Types.ObjectId
    },
    submittedDate: {
      type: Date
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
    editBy: {
      type: Schema.Types.ObjectId
    },
    isAdmin: {
      type: Boolean,
      default: true
    },
    imageArrId: {
      type: Array
    }
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
