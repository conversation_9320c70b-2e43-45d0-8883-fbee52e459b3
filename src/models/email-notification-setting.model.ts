// damage-report-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'emailNotificationSetting';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema(
    {
      type: {
        type: String,
        enum: ['geofencing-notification', 'sharing-notification'],
        default: 'geofencing-notification',
        required: true
      },
      updatedBy: {
        type: Schema.Types.ObjectId,
        required: true
      },
      createdBy: {
        type: Schema.Types.ObjectId,
        required: true
      },
      company: {
        type: Schema.Types.ObjectId,
        required: true
      },
      isEnable: {
        type: Boolean,
        default: false
      },
      emailReceiver: {
        type: [String],
        default: undefined,
        required: true
      }
    },
    {
      timestamps: true
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
