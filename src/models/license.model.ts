import { UserETS } from './../interface/user.d';
// license-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';


export default function (app: Application): Model<any> {
  const modelName = 'license';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const licenseUserSchema = new Schema({
    status: {
      type: Number
    },
    fullname: {
      type: String
    },
    username: {
      type: String, required: true
    },
    role: {
      type: Object
    },
    company: {
      type: Object
    },
    allowAccess: {
      type: Boolean,
      default: true
    },
  }, {
    timestamps: true
  });

  const schema = new Schema({
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    isAvailable: {
      type: Boolean,
      default: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
    companyId: {
      type: Schema.Types.ObjectId,
      required: true
    },

    licenseLimit: {
      type: Number,
      default: 10
    },
    users: {
      type: [licenseUserSchema],
      default: [],
    },
    trailersLimited: {
      type: Number,
      default: 100
    }
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
