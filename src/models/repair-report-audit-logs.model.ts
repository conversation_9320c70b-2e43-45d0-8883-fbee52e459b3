// repair-report-audit-logs-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'repairReportAuditLogs';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({
    repairReportId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    //this is report id at damagereport model
    damageReportId: {
      type: String
    },
    trailerId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    trailerNo: {
      type: String,
      required: true
    },
    companyId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    damageDescription: {
      type: String,
    },
    reportId: {
      type: String,
      required: true
    },
    reportDate: {
      type: Date,
      default: Date.now()
    },
    remarks: {
      type: String,
    },
    repairBy: {
      type: String,
    },
    repairDate: {
      type: Date
    },
    repairDateComplete: {
      type: Date
    },
    status: {
      type: Number,
      required: true
    },
    submittedBy: {
      type: Schema.Types.ObjectId
    },
    submittedDate: {
      type: Date
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
    editedBy: {
      type: Schema.Types.ObjectId
    },
    imageArrId: {
      type: Array
    }
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
