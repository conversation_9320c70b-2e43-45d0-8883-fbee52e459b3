// damage-report-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';
import configs from "../configs";

export default function (app: Application): Model<any> {
  const modelName = 'damageReport';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({
    lastRepairReportId: {
      type: Schema.Types.ObjectId
    },
    trailerId: {
      type: String,
      required: true
    },
    trailerNo: {
      type: String,
      required: true
    },
    companyId: {
      type: String,
      required: true
    },
    reportId: {
      type: String,
      required: true,
      unique: true
    },
    description: {
      type: String,
      max: 1000
    },
    status: {
      type: Number,
      required: true
    },
    reportDate: {
      type: Date,
      required: true
      // default: Date.now
    },
    submittedBy: {
      type: Schema.Types.ObjectId
    },
    submittedDate: {
      type: Date
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
    editBy: {
      type: Schema.Types.ObjectId
    },
    category: {
      type: String
    },
    isAdmin: {
      type: Boolean,
      default: true
    }
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
