// inspections.model.ts - Mongoose model for inspections

import { Application } from '../declarations';
import { Model, Mongoose, Schema, Document } from 'mongoose';

type InspectionType = keyof typeof CTVInspection.type;
type InspectionTypeValue = typeof CTVInspection.type[InspectionType];
const CTVInspection = {
  type: {
    HandOver: 'HandOver',
    TakeOver: 'TakeOver'
  }
}
export interface Inspection extends Document {
  type: InspectionTypeValue
  status: string
  trailerNumber: string
  damageReport: string
  inspector: {
    _id: string
    name: string
    email: string
    company: string
    vehicleNo: string
  }
  trailerRentalId: {
    type: Schema.Types.ObjectId
  }
  rentalId: {
    type: Schema.Types.ObjectId
  }
}

const inspectionSchema = {
  type: {
    type: String,
    enum: Object.values(CTVInspection.type),
    required: true
  },
  images: [
    {
      type: String
    }
  ],
  damageReport: {
    type: Schema.Types.ObjectId,
    required: false
  },
  inspector: {
    _id: { type: Schema.Types.ObjectId, required: true },
    name: { type: String, required: true },
    email: { type: String },
    vehicleNo: { type: String },
    company: { type: Schema.Types.ObjectId, required: true }
  },
  rentee: {
    name: { type: String, required: true },
    email: { type: String },
    company: { type: Schema.Types.ObjectId, required: true }
  },
  owner: {
    name: { type: String, required: true },
    email: { type: String },
    company: { type: Schema.Types.ObjectId, required: true }
  },
  trailerNumber: {
    type: String
  },
  rentalId: {
    type: Schema.Types.ObjectId,
    required: true
  },
  trailerRentalId: {
    type: Schema.Types.ObjectId
  }
}

export default function (app: Application): Model<Inspection> {
  const modelName = 'Inspection'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema(inspectionSchema, {
    timestamps: true
  })

  return mongooseClient.model<Inspection>(modelName, schema)
}

export { CTVInspection, inspectionSchema }
