// users-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'users';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({
    status: {
      type: Number
    },
    allowNotifications: {
      type: Boolean
    },
    preferredComm: {
      type: String
    },
    fullname: {
      type: String
    },
    username: {
      type: String
    },
    email: {
      type: String
    },
    mobile: {
      type: String
    },
    role: {
      type: Schema.Types.ObjectId
    },
    roleAcl: {
      type: Schema.Types.ObjectId
    },
    company: {
      type: Schema.Types.ObjectId
    },
    cpnRole: {
      type: Schema.Types.ObjectId
    },
    allowAccess: {
      type: <PERSON>olean,
      default: false
    },


  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
