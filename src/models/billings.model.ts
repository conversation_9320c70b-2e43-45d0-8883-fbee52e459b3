// billing.model.ts - Mongoose model for billing

import { Application } from '../declarations';
import { Model, Mongoose, Schema } from 'mongoose'
import { IBilling } from '../interface/billing'
import { rentalSchema } from './rentals.model'
import { inspectionSchema } from './inspections.model'

export const CTVBilling = {
  status: {
    Pending: 'Pending',
    Closed: 'Closed',
    Waived: 'Waived'
  }
}

export const billingSchema = {
  inspections: [inspectionSchema],
  rental: {
    type: rentalSchema,
    required: true
  },
  trailerRental: {
    ref: 'trailerRentalAvailability',
    type: Schema.Types.ObjectId
  },
  updatedBy: { type: Schema.Types.ObjectId },
  status: {
    type: String,
    enum: Object.values(CTVBilling.status),
    default: CTVBilling.status.Pending
  }
} as const

export default function (app: Application): Model<IBilling> {
  const modelName = 'billings';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema(billingSchema, {
    timestamps: true,
    versionKey: false,
  });

  return mongooseClient.model<IBilling>(modelName, schema);
}


