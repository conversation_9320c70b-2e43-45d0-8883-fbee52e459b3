// damage-report-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'notificationSetting';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({

    updatedBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
    company: {
      type: Schema.Types.ObjectId,
      required: true
    },
    isEnable: {
      type: Boolean,
      default: false
    },
    emailReceiver: {
      type: [String],
      default: undefined,
      required: true,
    },
    numOfRoadTaxExpiryDate: {
      type: Number,
      default: 1
    },
    numOfInsuranceExpiryDate: {
      type: Number,
      default: 1
    },
    numOfNextInspectionExpiryDate: {
      type: Number,
      default: 1
    },
    numOfRegistrationExpiryDate: {
      type: Number,
      default: 1
    },
    scheduler: {
      type: String,
      required: true,
      enum: ['Hourly', 'Daily', 'Monthly', 'Weekly', 'Fortnightly'],
    },
    history: {
      type: [Object],
    },
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
