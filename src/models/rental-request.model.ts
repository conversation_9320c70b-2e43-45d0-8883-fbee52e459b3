import { Application } from '../declarations'
import { Model, Mongoose, Schema, Document } from 'mongoose'
import { IRentalRequest } from '../interface/rental-request'

export default function(app: Application): Model<IRentalRequest> {
  const modelName = 'rentalRequest'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const { Schema } = mongooseClient

  const rentalRequestSchema = new Schema<IRentalRequest>({
    trailerRentalId: { type: Schema.Types.ObjectId, ref: "trailerRentalAvailability" },
    rentalId: { type: Schema.Types.ObjectId, ref: "rental" },
    trailerNumber: { type: String },
    requester: {
      name: { type: String, required: true },
      email: { type: String },
      company: { type: Schema.Types.ObjectId, required: true }
    },
    owner: {
      name: { type: String, required: true },
      email: { type: String },
      company: { type: Schema.Types.ObjectId, required: true }
    },
    updatedBy: { type: Schema.Types.ObjectId },
    createdBy: { type: Schema.Types.ObjectId },
    requestStart: { type: Date, required: true },
    requestEnd: { type: Date, required: true },
    rejectReason: { type: String },
    message: { type: String },
    status: {
      type: String,
      enum: Object.values(CTVRentalRequest.status),
      default: Object.values(CTVRentalRequest.status)[0]
    },
    requestType: {
      type: String,
      enum: Object.values(CTVRentalRequest.type),
      default: Object.values(CTVRentalRequest.type)[0]
    },
    estimatedAmount: { type: Number },
    numberOfDays: { type: Number },
    rentingDetails: {
      type: {
        dailyPrice: { type: Number },
        monthlyPrice: { type: Number },
        // availableFrom: {type: Date},
        // availableTo: {type: Date}
      }
    },
    assignee: { type: Object }
  }, {
    timestamps: true
  })

  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName)
  }

  return mongooseClient.model<IRentalRequest>(modelName, rentalRequestSchema)
}

const CTVRentalRequest = {
  type: {
    New: 'New',
    Extension: 'Extended',
    ChangeRequest: 'ChangeRequest',
  },
  status: {
    Pending: 'Pending',
    Approved: 'Approved',
    Rejected: 'Rejected',
    Canceled: 'Canceled',
    Started: 'Started'
  }
}
export { CTVRentalRequest }
