import { Application } from '../declarations'
import { Model, Mongoose, ObjectId } from 'mongoose'
import { ITrailerRentalAvailability } from '../interface/trailer-rental-availability'


export default function(app: Application): Model<ITrailerRentalAvailability> {
  const modelName = 'trailerRentalAvailability'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const { Schema } = mongooseClient

  const schema = new Schema<ITrailerRentalAvailability>({
    trailer: {
      type: Object,
      required: true
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    trailerNumber: {
      type: String
    },
    availableForRentTo: [{
      trailerSharingId: {type: Schema.Types.ObjectId,},
      companyId:  {type: Schema.Types.ObjectId,},
      isEnable: {
        type: Boolean,
        default: true
      }
    }],
    rentingDetails: {
      dailyPrice: {
        type: Number,
        required: true,
        default: 0
      },
      monthlyPrice: {
        type: Number,
        required: true,
      },
    },
    isAvailable: {
      type: Boolean,
      default: true
    },
    isLeasing: {
      type: Boolean,
      default: false
    },
    createdBy: {
      type: Schema.Types.ObjectId,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
    },
    deletedAt: {
      type: Date
    },
    rentalHistory: [{
      type: Schema.Types.ObjectId,
      ref: "rental"
    }]
  }, {
    timestamps: true
  })

  schema.index({ 'trailer.trailerNumber': 1 }, { unique: true })
  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName)
  }

  return mongooseClient.model<ITrailerRentalAvailability>(modelName, schema)
}
