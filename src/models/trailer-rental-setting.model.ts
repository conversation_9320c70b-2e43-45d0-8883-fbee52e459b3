import { Application } from '../declarations';
import { Document, Model, Mongoose, Schema } from 'mongoose'
import { ITrailerRentalSetting } from '../interface/trailer-rental-setting'

export default function (app: Application): Model<ITrailerRentalSetting> {
  const modelName = 'trailerRentalSetting';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;

  const schema = new Schema<ITrailerRentalSetting>({
    company: {
      type: Schema.Types.ObjectId,
      unique: true,
      required: true
    },
    autoAcceptList: [{
      type: Schema.Types.ObjectId,
    }],
    whiteList: {
      type: Object,
    },
    isAutoAccept: {
      type: Boolean,
      default: true
    }
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }

  return mongooseClient.model<ITrailerRentalSetting>(modelName, schema);
}
