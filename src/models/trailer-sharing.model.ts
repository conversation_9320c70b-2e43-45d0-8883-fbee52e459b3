// trailer-sharing-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations'
import { Model, Mongoose } from 'mongoose'
import { ITrailerSharing } from '../interface/trailer-sharing'

export default function(app: Application): Model<ITrailerSharing> {
  const modelName = 'trailerSharing2'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    companyId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    trailerNos: {
      type: [String]
    },
    trailerIds: {
      type: [String]
    },
    sharedCompany: {
      type: Schema.Types.ObjectId
    },
    trailers: [{
      trailerNo: {
        type: String,
        required: true
      },
      rentingDetails: {
        dailyPrice: {
          type: Number,
          required: true,
          default: 0
        },
        monthlyPrice: {
          type: Number,
          required: true,
        },
      },
      isEnable: {
        type: Boolean,
        default: true
      }
    }],
    isAvailable: {
      type: Boolean,
      default: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
    },
    updatedBy:{
      type: Schema.Types.ObjectId,
    },
  }, {
    timestamps: true
  })
  schema.index({ companyId: 1, sharedCompany: 1 }, { unique: true });
  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
