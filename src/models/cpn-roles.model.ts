// role.model.ts - Mongoose model for role

import { Application } from '../declarations'
import { Model, Mongoose, Schema } from 'mongoose'
import { IRole } from '../interface/role'

export const roleSchema = {
  name: { type: String },
  modules: [
    {
      _id: false,
      moduleName: { type: String, required: true },
      allowCreate: { type: Boolean, default: false },
      allowRead: { type: Boolean, default: false },
      allowUpdate: { type: Boolean, default: false },
      allowDelete: { type: Boolean, default: false },
      invisible: { type: [String] },
      additional: { type: Schema.Types.Mixed }
    }
  ],
  users: { type: [Schema.Types.ObjectId] },
  company: { type: Schema.Types.ObjectId, required: true },
  updatedBy: { type: Schema.Types.ObjectId },
  createdBy: { type: Schema.Types.ObjectId },
  deletedBy: { type: Schema.Types.ObjectId },
  deletedAt: { type: Date }
}

export default function (app: Application): Model<IRole> {
  const modelName = 'ets-roles'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema(roleSchema, {
    timestamps: true
  })

  return mongooseClient.model<IRole>(modelName, schema)
}
