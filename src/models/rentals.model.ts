import { Application } from '../declarations'
import { Model, Mongoose, Schema, Document } from 'mongoose'
import { CTVInspection } from './inspections.model'
import { IRental } from '../interface/rental'

const CTVRental = {
  status: {
    UpComing: 'UpComing',
    PendingConfirmationByRentee: 'PendingConfirmationByRentee',
    // Ready: 'Ready', //Ready status is not used from 18/10/2023,
    Ready: 'NotStarted',
    NotStarted: 'NotStarted',
    InProgress: 'Started',
    Rejected: 'RejectedByCustomer',
    Returned: 'Returned',
    Completed: 'Completed',
    Canceled: 'CanceledByOwner'
  },
  statusTransitions: {
    UpComing: ['PendingConfirmationByRentee', 'Ready', 'NotStarted', 'CanceledByOwner', 'RejectedByCustomer'],
    PendingConfirmationByRentee: ['NotStarted', 'Ready', 'RejectedByCustomer', 'CanceledByOwner'],
    // Ready: ['NotStarted', 'Started', 'RejectedByCustomer', 'CanceledByOwner'],
    NotStarted: ['Started','Ready', 'RejectedByCustomer', 'CanceledByOwner'],
    Started: ['Returned'],
    Returned: ['Completed'],
    Completed: [],
    RejectedByCustomer: [],
    CanceledByOwner: []
  } as { [key: string]: string[] },

  statusPermission: {
    UpComing: ['owner'],
    PendingConfirmationByRentee: ['owner'],
    // Ready: ['owner'],
    NotStarted: ['owner'],
    Started: ['rentee'],
    Returned: ['rentee'],
    Completed: ['owner'],
    RejectedByCustomer: ['rentee'],
    CanceledByOwner: ['owner']
  } as { [key: string]: string[] },

  getEndStatus: function() {
    return [this.status.Completed, this.status.Rejected, this.status.Canceled]
  },
  getProcessingStatus: function() {
    return [this.status.UpComing, this.status.PendingConfirmationByRentee, this.status.NotStarted, this.status.Ready, this.status.InProgress, this.status.Returned]
  },
  isStatusAllowInspection: function(status: string, type: string) {
    if (status == this.status.Ready) return type == 'TakeOver'
    if (status == this.status.InProgress) return type == 'HandOver'
    return false
  },
  isEndStatus: function(status: string) {
    return this.getEndStatus().includes(status)
  }

}

const rentalSchema = {
  trailerRentalId: {
    type: Schema.Types.ObjectId,
    ref: 'trailerRentalAvailability',
    required: true
  },
  trailerId: {
    type: Schema.Types.ObjectId
  },
  rentee: {
    name: { type: String, required: true },
    email: { type: String },
    company: { type: Schema.Types.ObjectId, required: true }
  },
  owner: {
    name: { type: String, required: true },
    email: { type: String },
    company: { type: Schema.Types.ObjectId, required: true }
  },
  start: { type: Date, required: true },
  end: { type: Date, required: true },
  status: { type: String, enum: Object.values(CTVRental.status), default: CTVRental.status.Ready },
  rentingDetails: {
    dailyPrice: {
      type: Number,
    },
    monthlyPrice: {
      type: Number,
    }
  },
  estimatedAmount: { type: Number },
  numberOfDays: { type: Number },
  trailerNumber: { type: String },
  assignee: { type: Object },
  requestId: {
    type: Schema.Types.ObjectId,
    ref: 'rentalRequest',
  },
  history: [
    {
      original: { type: Object, required: true },
      updated: { type: Object, required: true },
      updatedBy: { type: Object, required: true },
      timestamp: { type: Date, default: Date.now }
    }
  ],
  createdBy: {
    type: Schema.Types.ObjectId,
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
  },
  deletedBy: {
    type: Schema.Types.ObjectId,
  },
  deletedAt: {
    type: Date
  },
  changeRequest: {
    type: Object,
  }
}


export default function(app: Application): Model<IRental> {
  const modelName = 'rental'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const { Schema } = mongooseClient

  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName)
  }

  const schema = new Schema<IRental>(rentalSchema, {
    timestamps: true
  })

  return mongooseClient.model<IRental>(modelName, schema)
}


export { CTVRental, rentalSchema }
