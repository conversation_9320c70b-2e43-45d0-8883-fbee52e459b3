// zoneManagement-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';
import config from '../configs'

export default function (app: Application): Model<any> {
  const modelName = 'zoneManagement';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({
    companyId: {
      type: Schema.Types.ObjectId,
    },
    zoneName: {
      type: String,
      required: true
    },
    zoneInstance: {
      type: String,
      required: true
    },
    geometry: {
      type: {
        type: String,
        enum: ['Polygon'],
        required: true
      },
      coordinates: {
        type: [[[Number]]], // Array of arrays of arrays of numbers
        required: true
      }
    },
    isAdminZone: {
      type: Boolean,
      default: false
    },
    status: {
      type: Number,
      default: config.statusZone.active
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true
    },
  }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
