// trailer-allocation-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import { Application } from '../declarations';
import { Model, Mongoose } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'trailerAllocation';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;
  const schema = new Schema({
    trailerId: {
      type: String
    },
    company: {
      type: Schema.Types.ObjectId,
    },
    onLease: {
      type: Boolean,
      required: true
    },
    isPairingRequired: {
      type: Boolean,
    },
    billable: {
      type: <PERSON>olean,
    },
    trailerNumber: {
      type: String,
    },
    truckNumber: {
      type: JSON
    },
    pairedBy: {
      type: String
    },
    pairingDate: {
      type: Date
    },
    allocatedBy: {
      type: Schema.Types.ObjectId
    },
    createdBy: {
      type: Schema.Types.ObjectId
    },
    updatedBy: {
      type: Schema.Types.ObjectId
    },
    }, {
    timestamps: true
  });

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }
  return mongooseClient.model<any>(modelName, schema);
}
