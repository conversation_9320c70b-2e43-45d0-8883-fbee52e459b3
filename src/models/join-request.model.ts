import { Application } from '../declarations';
import { Model, Mongoose, Schema, Document } from 'mongoose';

type JoinRequestType = keyof typeof CTVJoinRequest.status;
type JoinRequestTypeValue = typeof CTVJoinRequest.status[JoinRequestType];


interface IJoinRequest extends Document {
  company: Schema.Types.ObjectId;
  timeSubmit: Date;
  tncVersion: string;
  reviewTime: Date;
  rejectReason: string;
  status: JoinRequestTypeValue;
}

export default function (app: Application): Model<IJoinRequest> {
  const modelName = 'joinRequest';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;

  const joinRequestSchema = new Schema<IJoinRequest>({
    company: { type: Schema.Types.ObjectId, required: true },
    reviewTime: { type: Date},
    rejectReason: { type: String},
    tncVersion: { type: String, required: true },
    status: { type: String, enum: Object.values(CTVJoinRequest.status), default: Object.values(CTVJoinRequest.status)[0] },
  }, {
    timestamps: true
  });

  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }

  return mongooseClient.model<IJoinRequest>(modelName, joinRequestSchema);
}

const CTVJoinRequest = {
  status: {
    Pending: 'Pending',
    Approved: 'Approved',
    Rejected: 'Rejected'
  }
}

export  {
  CTVJoinRequest
}
