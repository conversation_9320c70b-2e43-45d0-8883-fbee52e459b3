import { Application } from '../declarations';
import { Model, Mongoose, Schema, Document } from 'mongoose';

export default function (app: Application): Model<any> {
  const modelName = 'dynamicBlacklist';
  const mongooseClient: Mongoose = app.get('mongooseClient');
  const { Schema } = mongooseClient;

  const dynamicBlacklistSchema = new Schema<any>({
    company: { type: Schema.Types.ObjectId, required: true },
  }, {
    timestamps: true
  });

  if (mongooseClient.modelNames().includes(modelName)) {
    (mongooseClient as any).deleteModel(modelName);
  }

  return mongooseClient.model<any>(modelName, dynamicBlacklistSchema);
}
