const rentalPaths = {
  '/rental': {
    get: {
      tags: ['rentals'],
      summary: 'Get all rentals',
      parameters: [
        {
          name: 'limit',
          in: 'query',
          description: 'Limit the number of results returned',
          schema: {
            type: 'integer',
            minimum: 1,
            default: 20
          }
        },
        {
          name: 'skip',
          in: 'query',
          description: 'Skip the first N results',
          schema: {
            type: 'integer',
            minimum: 0,
            default: 0
          }
        },
        { name: "Authorization", in: "header", type: "string", description: "auth token" }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  data: {
                    type: 'array',
                    items: {
                      $ref: '#/components/schemas/Rental'
                    }
                  }
                }
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    post: {
      parameters: [{ name: "Authorization", in: "header", type: "string", description: "auth token" }],
      tags: ['rentals'],
      summary: 'Create a new rental',
      security: [
        { "Bearer": [] }
      ],
      requestBody: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Rental'
            }
          }
        },
        required: true
      },
      responses: {
        '201': {
          description: 'Created',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Rental'
              }
            }
          }
        },
        '400': {
          description: 'Bad request'
        },
        '401': {
          description: 'Unauthorized'
        },
        '500': {
          description: 'Server error'
        }
      }
    }
  },
  '/rental/{id}': {
    get: {
      tags: ['rentals'],
      summary: 'Get a rental by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to retrieve',
          required: true,
          schema: {
            type: 'string'
          }
        },
        { name: "Authorization", in: "header", type: "string", description: "auth token" }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Rental'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    put: {
      tags: ['rentals'],
      summary: 'Update a rental by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to update',
          required: true,
          schema: {
            type: 'string'
          }
        },
        {
          name: "Authorization",
          in: "header",
          type: "string",
          description: "auth token"
        },
        {
          name: 'body',
          in: 'body',
          description: 'Updated rental object',
          required: true,
          schema: {
            $ref: '#/components/schemas/Rental'
          }
        }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Rental'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    patch: {
      tags: ['rentals'],
      summary: 'Update a rental by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to update',
          required: true,
          schema: {
            type: 'string'
          }
        },
        {
          name: "Authorization",
          in: "header",
          type: "string",
          description: "auth token"
        },
        {
          name: 'body',
          in: 'body',
          description: 'Updated rental object',
          required: true,
          schema: {
            $ref: '#/components/schemas/Rental'
          }
        }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Rental'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
  }
}

const rentalRequestPaths = {
  '/rental-request': {
    get: {
      tags: ['rental requests'],
      summary: 'Get all rental requests',
      parameters: [
        {
          name: 'limit',
          in: 'query',
          description: 'Limit the number of results returned',
          schema: {
            type: 'integer',
            minimum: 1,
            default: 20
          }
        },
        {
          name: 'skip',
          in: 'query',
          description: 'Skip the first N results',
          schema: {
            type: 'integer',
            minimum: 0,
            default: 0
          }
        },
        { name: "Authorization", in: "header", type: "string", description: "auth token" }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  data: {
                    type: 'array',
                    items: {
                      $ref: '#/components/schemas/RentalRequest'
                    }
                  }
                }
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    post: {
      parameters: [{ name: "Authorization", in: "header", type: "string", description: "auth token" }],
      tags: ['rental requests'],
      summary: 'Create a new rental request',
      security: [
        { "Bearer": [] }
      ],
      requestBody: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/RentalRequest'
            }
          }
        },
        required: true
      },
      responses: {
        '201': {
          description: 'Created',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '400': {
          description: 'Bad request'
        },
        '401': {
          description: 'Unauthorized'
        },
        '500': {
          description: 'Server error'
        }
      }
    }
  },
  '/rental-request/{id}': {
    get: {
      tags: ['rental requests'],
      summary: 'Get a rental request by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental request to retrieve',
          required: true,
          schema: {
            type: 'string'
          }
        },
        { name: "Authorization", in: "header", type: "string", description: "auth token" }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    put: {
      tags: ['rental requests'],
      summary: 'Update a rental request by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to update',
          required: true,
          schema: {
            type: 'string'
          }
        },
        {
          name: "Authorization",
          in: "header",
          type: "string",
          description: "auth token"
        },
        {
          name: 'body',
          in: 'body',
          description: 'Updated rental object',
          required: true,
          schema: {
            $ref: '#/components/schemas/RentalRequest'
          }
        }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    patch: {
      tags: ['rental requests'],
      summary: 'Update a rental request by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to update',
          required: true,
          schema: {
            type: 'string'
          }
        },
        {
          name: "Authorization",
          in: "header",
          type: "string",
          description: "auth token"
        },
        {
          name: 'body',
          in: 'body',
          description: 'Updated rental object',
          required: true,
          schema: {
            $ref: '#/components/schemas/RentalRequest'
          }
        }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
  }
}

const trailerRentalPath = {
  '/trailer-rental': {
    get: {
      tags: ['trailer rental'],
      summary: 'Get all trailer rental',
      parameters: [
        {
          name: 'limit',
          in: 'query',
          description: 'Limit the number of results returned',
          schema: {
            type: 'integer',
            minimum: 1,
            default: 20
          }
        },
        {
          name: 'skip',
          in: 'query',
          description: 'Skip the first N results',
          schema: {
            type: 'integer',
            minimum: 0,
            default: 0
          }
        },
        { name: "Authorization", in: "header", type: "string", description: "auth token" }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  data: {
                    type: 'array',
                    items: {
                      $ref: '#/components/schemas/TrailerRental'
                    }
                  }
                }
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    post: {
      parameters: [{ name: "Authorization", in: "header", type: "string", description: "auth token" }],
      tags: ['trailer rental'],
      summary: 'Create a new trailer rental',
      security: [
        { "Bearer": [] }
      ],
      requestBody: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/TrailerRental'
            }
          }
        },
        required: true
      },
      responses: {
        '201': {
          description: 'Created',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '400': {
          description: 'Bad request'
        },
        '401': {
          description: 'Unauthorized'
        },
        '500': {
          description: 'Server error'
        }
      }
    }
  },
  '/trailer-rental/{id}': {
    get: {
      tags: ['trailer rental'],
      summary: 'Get a rental request by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental request to retrieve',
          required: true,
          schema: {
            type: 'string'
          }
        },
        { name: "Authorization", in: "header", type: "string", description: "auth token" }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    put: {
      tags: ['trailer rental'],
      summary: 'Update a trailer rental by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to update',
          required: true,
          schema: {
            type: 'string'
          }
        },
        {
          name: "Authorization",
          in: "header",
          type: "string",
          description: "auth token"
        },
        {
          name: 'body',
          in: 'body',
          description: 'Updated trailer rental object',
          required: true,
          schema: {
            $ref: '#/components/schemas/TrailerRental'
          }
        }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
    patch: {
      tags: ['trailer rental'],
      summary: 'Update a trailer rental by ID',
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: 'ID of the rental to update',
          required: true,
          schema: {
            type: 'string'
          }
        },
        {
          name: "Authorization",
          in: "header",
          type: "string",
          description: "auth token"
        },
        {
          name: 'body',
          in: 'body',
          description: 'Updated trailer rental object',
          required: true,
          schema: {
            $ref: '#/components/schemas/TrailerRental'
          }
        }
      ],
      security: [
        {
          BearerAuth: []
        }
      ],
      responses: {
        '200': {
          description: 'OK',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RentalRequest'
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized'
        },
        '404': {
          description: 'Not found'
        },
        '500': {
          description: 'Server error'
        }
      }
    },
  }
}

module.exports = {
  ...rentalPaths,
  ...rentalRequestPaths,
  ...trailerRentalPath
}
